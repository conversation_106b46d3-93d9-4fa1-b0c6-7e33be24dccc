name: foodflow
description: A cross-platform food delivery app with role-based access.
version: 1.0.0+1
publish_to: 'none'

environment:
  sdk: '>=3.0.0 <4.0.0'

dependencies:
  flutter:
    sdk: flutter
  flutter_localizations:
    sdk: flutter
  intl: ^0.19.0

  # State Management
  flutter_riverpod: ^2.4.9
  riverpod_annotation: ^2.3.3

  # Navigation
  go_router: ^13.0.0

  # UI Components
  flutter_svg: ^2.0.10+1
  cached_network_image: ^3.3.0
  shimmer: ^3.0.0
  flutter_spinkit: ^5.2.0
  badges: ^3.0.2

  # Form & Validation
  flutter_form_builder: ^9.3.1

  # State Persistence
  shared_preferences: ^2.2.2

  # Supabase
  supabase_flutter: ^2.5.6

  # Maps & Location
  google_maps_flutter: ^2.5.0
  geolocator: ^10.1.0

  # Notifications
  flutter_local_notifications: ^16.3.2

  # Utils
  intl_phone_field: ^3.1.0
  image_picker: ^1.0.7
  url_launcher: ^6.2.2
  uuid: ^4.2.2
  google_fonts: ^6.2.1
  sizer: ^3.0.0

dev_dependencies:
  flutter_test:
    sdk: flutter
  flutter_lints: ^3.0.1
  build_runner: ^2.4.6
  riverpod_generator: ^2.3.9
  mocktail: ^1.0.3

flutter:
  uses-material-design: true
  generate: true

  assets:
    - assets/images/
-- Initial FoodFlow database schema with authentication and role-based access
-- This migration creates the complete database structure for the food delivery platform

-- 1. <PERSON>reate custom types
CREATE TYPE public.user_role AS ENUM ('super_admin', 'restaurant_admin', 'rider', 'customer');
CREATE TYPE public.order_status AS ENUM ('pending', 'confirmed', 'preparing', 'ready', 'picked_up', 'delivered', 'cancelled');
CREATE TYPE public.rider_status AS ENUM ('offline', 'available', 'busy');
CREATE TYPE public.restaurant_status AS ENUM ('open', 'closed', 'busy');

-- 2. Create user_profiles table as intermediary for auth
CREATE TABLE public.user_profiles (
    id UUID PRIMARY KEY REFERENCES auth.users(id) ON DELETE CASCADE,
    email TEXT NOT NULL UNIQUE,
    full_name TEXT NOT NULL,
    role public.user_role DEFAULT 'customer'::public.user_role,
    phone TEXT,
    avatar_url TEXT,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- 3. Create restaurants table
CREATE TABLE public.restaurants (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    owner_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    description TEXT,
    address TEXT NOT NULL,
    phone TEXT,
    email TEXT,
    image_url TEXT,
    status public.restaurant_status DEFAULT 'open'::public.restaurant_status,
    rating DECIMAL(3,2) DEFAULT 0.0,
    delivery_fee DECIMAL(10,2) DEFAULT 0.0,
    minimum_order DECIMAL(10,2) DEFAULT 0.0,
    delivery_time_minutes INTEGER DEFAULT 30,
    cuisine_type TEXT,
    is_featured BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- 4. Create menu_items table
CREATE TABLE public.menu_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    restaurant_id UUID REFERENCES public.restaurants(id) ON DELETE CASCADE,
    name TEXT NOT NULL,
    description TEXT,
    price DECIMAL(10,2) NOT NULL,
    image_url TEXT,
    category TEXT NOT NULL,
    is_available BOOLEAN DEFAULT true,
    preparation_time_minutes INTEGER DEFAULT 15,
    calories INTEGER,
    ingredients TEXT[],
    allergens TEXT[],
    is_vegetarian BOOLEAN DEFAULT false,
    is_vegan BOOLEAN DEFAULT false,
    is_spicy BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- 5. Create orders table
CREATE TABLE public.orders (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    customer_id UUID REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    restaurant_id UUID REFERENCES public.restaurants(id) ON DELETE CASCADE,
    rider_id UUID REFERENCES public.user_profiles(id) ON DELETE SET NULL,
    order_number TEXT NOT NULL UNIQUE,
    status public.order_status DEFAULT 'pending'::public.order_status,
    subtotal DECIMAL(10,2) NOT NULL,
    delivery_fee DECIMAL(10,2) NOT NULL,
    tax_amount DECIMAL(10,2) NOT NULL,
    total_amount DECIMAL(10,2) NOT NULL,
    delivery_address TEXT NOT NULL,
    delivery_instructions TEXT,
    payment_method TEXT,
    payment_status TEXT DEFAULT 'pending',
    estimated_delivery_time TIMESTAMPTZ,
    actual_delivery_time TIMESTAMPTZ,
    special_instructions TEXT,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- 6. Create order_items table
CREATE TABLE public.order_items (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    order_id UUID REFERENCES public.orders(id) ON DELETE CASCADE,
    menu_item_id UUID REFERENCES public.menu_items(id) ON DELETE CASCADE,
    quantity INTEGER NOT NULL DEFAULT 1,
    unit_price DECIMAL(10,2) NOT NULL,
    total_price DECIMAL(10,2) NOT NULL,
    special_instructions TEXT,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- 7. Create rider_profiles table for additional rider info
CREATE TABLE public.rider_profiles (
    id UUID PRIMARY KEY REFERENCES public.user_profiles(id) ON DELETE CASCADE,
    vehicle_type TEXT,
    license_number TEXT,
    status public.rider_status DEFAULT 'offline'::public.rider_status,
    current_latitude DECIMAL(10,8),
    current_longitude DECIMAL(11,8),
    total_deliveries INTEGER DEFAULT 0,
    total_earnings DECIMAL(10,2) DEFAULT 0.0,
    rating DECIMAL(3,2) DEFAULT 0.0,
    is_verified BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
);

-- 8. Create essential indexes
CREATE INDEX idx_user_profiles_role ON public.user_profiles(role);
CREATE INDEX idx_user_profiles_email ON public.user_profiles(email);
CREATE INDEX idx_restaurants_owner_id ON public.restaurants(owner_id);
CREATE INDEX idx_restaurants_status ON public.restaurants(status);
CREATE INDEX idx_menu_items_restaurant_id ON public.menu_items(restaurant_id);
CREATE INDEX idx_menu_items_category ON public.menu_items(category);
CREATE INDEX idx_orders_customer_id ON public.orders(customer_id);
CREATE INDEX idx_orders_restaurant_id ON public.orders(restaurant_id);
CREATE INDEX idx_orders_rider_id ON public.orders(rider_id);
CREATE INDEX idx_orders_status ON public.orders(status);
CREATE INDEX idx_orders_created_at ON public.orders(created_at);
CREATE INDEX idx_order_items_order_id ON public.order_items(order_id);
CREATE INDEX idx_rider_profiles_status ON public.rider_profiles(status);

-- 9. Enable RLS
ALTER TABLE public.user_profiles ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.restaurants ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.menu_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.orders ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.order_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.rider_profiles ENABLE ROW LEVEL SECURITY;

-- 10. Helper functions for RLS policies
CREATE OR REPLACE FUNCTION public.is_super_admin()
RETURNS BOOLEAN
LANGUAGE sql
STABLE
SECURITY DEFINER
AS $$
SELECT EXISTS (
    SELECT 1 FROM public.user_profiles up
    WHERE up.id = auth.uid() AND up.role = 'super_admin'
)
$$;

CREATE OR REPLACE FUNCTION public.is_restaurant_owner(restaurant_uuid UUID)
RETURNS BOOLEAN
LANGUAGE sql
STABLE
SECURITY DEFINER
AS $$
SELECT EXISTS (
    SELECT 1 FROM public.restaurants r
    WHERE r.id = restaurant_uuid AND r.owner_id = auth.uid()
)
$$;

CREATE OR REPLACE FUNCTION public.can_access_order(order_uuid UUID)
RETURNS BOOLEAN
LANGUAGE sql
STABLE
SECURITY DEFINER
AS $$
SELECT EXISTS (
    SELECT 1 FROM public.orders o
    JOIN public.user_profiles up ON up.id = auth.uid()
    WHERE o.id = order_uuid AND (
        o.customer_id = auth.uid() OR
        o.rider_id = auth.uid() OR
        (up.role = 'restaurant_admin' AND o.restaurant_id IN (
            SELECT r.id FROM public.restaurants r WHERE r.owner_id = auth.uid()
        )) OR
        up.role = 'super_admin'
    )
)
$$;

CREATE OR REPLACE FUNCTION public.can_access_menu_item(item_uuid UUID)
RETURNS BOOLEAN
LANGUAGE sql
STABLE
SECURITY DEFINER
AS $$
SELECT EXISTS (
    SELECT 1 FROM public.menu_items mi
    JOIN public.restaurants r ON mi.restaurant_id = r.id
    JOIN public.user_profiles up ON up.id = auth.uid()
    WHERE mi.id = item_uuid AND (
        r.owner_id = auth.uid() OR
        up.role = 'super_admin'
    )
)
$$;

-- 11. RLS Policies
-- User profiles policies
CREATE POLICY "users_view_own_profile" ON public.user_profiles FOR SELECT
USING (auth.uid() = id OR public.is_super_admin());

CREATE POLICY "users_update_own_profile" ON public.user_profiles FOR UPDATE
USING (auth.uid() = id OR public.is_super_admin())
WITH CHECK (auth.uid() = id OR public.is_super_admin());

CREATE POLICY "super_admin_manage_users" ON public.user_profiles FOR ALL
USING (public.is_super_admin())
WITH CHECK (public.is_super_admin());

-- Restaurants policies
CREATE POLICY "public_view_restaurants" ON public.restaurants FOR SELECT
TO public USING (true);

CREATE POLICY "restaurant_owners_manage_own" ON public.restaurants FOR ALL
USING (public.is_restaurant_owner(id) OR public.is_super_admin())
WITH CHECK (public.is_restaurant_owner(id) OR public.is_super_admin());

-- Menu items policies
CREATE POLICY "public_view_menu_items" ON public.menu_items FOR SELECT
TO public USING (true);

CREATE POLICY "restaurant_owners_manage_menu" ON public.menu_items FOR ALL
USING (public.can_access_menu_item(id))
WITH CHECK (public.can_access_menu_item(id));

-- Orders policies
CREATE POLICY "users_access_own_orders" ON public.orders FOR ALL
USING (public.can_access_order(id))
WITH CHECK (public.can_access_order(id));

-- Order items policies
CREATE POLICY "users_access_order_items" ON public.order_items FOR ALL
USING (EXISTS (
    SELECT 1 FROM public.orders o
    WHERE o.id = order_id AND public.can_access_order(o.id)
))
WITH CHECK (EXISTS (
    SELECT 1 FROM public.orders o
    WHERE o.id = order_id AND public.can_access_order(o.id)
));

-- Rider profiles policies
CREATE POLICY "riders_manage_own_profile" ON public.rider_profiles FOR ALL
USING (auth.uid() = id OR public.is_super_admin())
WITH CHECK (auth.uid() = id OR public.is_super_admin());

CREATE POLICY "public_view_available_riders" ON public.rider_profiles FOR SELECT
TO public USING (status = 'available');

-- 12. Functions for automatic profile creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER
SECURITY DEFINER
LANGUAGE plpgsql
AS $$
BEGIN
  INSERT INTO public.user_profiles (id, email, full_name, role)
  VALUES (
    NEW.id, 
    NEW.email, 
    COALESCE(NEW.raw_user_meta_data->>'full_name', split_part(NEW.email, '@', 1)),
    COALESCE(NEW.raw_user_meta_data->>'role', 'customer')::public.user_role
  );
  
  -- Create rider profile if user is a rider
  IF (NEW.raw_user_meta_data->>'role' = 'rider') THEN
    INSERT INTO public.rider_profiles (id) VALUES (NEW.id);
  END IF;
  
  RETURN NEW;
END;
$$;

-- Trigger for new user creation
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- 13. Functions for updating timestamps
CREATE OR REPLACE FUNCTION public.update_updated_at()
RETURNS TRIGGER
LANGUAGE plpgsql
AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$;

-- Create triggers for updated_at
CREATE TRIGGER update_user_profiles_updated_at
    BEFORE UPDATE ON public.user_profiles
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at();

CREATE TRIGGER update_restaurants_updated_at
    BEFORE UPDATE ON public.restaurants
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at();

CREATE TRIGGER update_menu_items_updated_at
    BEFORE UPDATE ON public.menu_items
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at();

CREATE TRIGGER update_orders_updated_at
    BEFORE UPDATE ON public.orders
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at();

CREATE TRIGGER update_rider_profiles_updated_at
    BEFORE UPDATE ON public.rider_profiles
    FOR EACH ROW EXECUTE FUNCTION public.update_updated_at();

-- 14. Function to generate unique order numbers
CREATE OR REPLACE FUNCTION public.generate_order_number()
RETURNS TEXT
LANGUAGE plpgsql
AS $$
DECLARE
    order_num TEXT;
    prefix TEXT := 'FF';
    date_part TEXT := TO_CHAR(NOW(), 'YYMMDD');
    sequence_part TEXT;
    counter INTEGER;
BEGIN
    -- Get the count of orders created today
    SELECT COUNT(*) INTO counter
    FROM public.orders
    WHERE DATE(created_at) = CURRENT_DATE;
    
    -- Increment counter and format with leading zeros
    counter := counter + 1;
    sequence_part := LPAD(counter::TEXT, 4, '0');
    
    -- Combine parts
    order_num := prefix || date_part || sequence_part;
    
    RETURN order_num;
END;
$$;

-- 15. Mock data for development and testing
DO $$
DECLARE
    admin_uuid UUID := gen_random_uuid();
    restaurant_owner_uuid UUID := gen_random_uuid();
    rider_uuid UUID := gen_random_uuid();
    customer_uuid UUID := gen_random_uuid();
    restaurant_uuid UUID := gen_random_uuid();
    pizza_item_uuid UUID := gen_random_uuid();
    burger_item_uuid UUID := gen_random_uuid();
    order_uuid UUID := gen_random_uuid();
BEGIN
    -- Create auth users with complete field structure
    INSERT INTO auth.users (
        id, instance_id, aud, role, email, encrypted_password, email_confirmed_at,
        created_at, updated_at, raw_user_meta_data, raw_app_meta_data,
        is_sso_user, is_anonymous, confirmation_token, confirmation_sent_at,
        recovery_token, recovery_sent_at, email_change_token_new, email_change,
        email_change_sent_at, email_change_token_current, email_change_confirm_status,
        reauthentication_token, reauthentication_sent_at, phone, phone_change,
        phone_change_token, phone_change_sent_at
    ) VALUES
        (admin_uuid, '00000000-0000-0000-0000-000000000000', 'authenticated', 'authenticated',
         '<EMAIL>', crypt('admin123', gen_salt('bf', 10)), now(), now(), now(),
         '{"full_name": "Super Admin", "role": "super_admin"}'::jsonb, '{"provider": "email", "providers": ["email"]}'::jsonb,
         false, false, '', null, '', null, '', '', null, '', 0, '', null, null, '', '', null),
        (restaurant_owner_uuid, '00000000-0000-0000-0000-000000000000', 'authenticated', 'authenticated',
         '<EMAIL>', crypt('restaurant123', gen_salt('bf', 10)), now(), now(), now(),
         '{"full_name": "Restaurant Manager", "role": "restaurant_admin"}'::jsonb, '{"provider": "email", "providers": ["email"]}'::jsonb,
         false, false, '', null, '', null, '', '', null, '', 0, '', null, null, '', '', null),
        (rider_uuid, '00000000-0000-0000-0000-000000000000', 'authenticated', 'authenticated',
         '<EMAIL>', crypt('rider123', gen_salt('bf', 10)), now(), now(), now(),
         '{"full_name": "Delivery Rider", "role": "rider"}'::jsonb, '{"provider": "email", "providers": ["email"]}'::jsonb,
         false, false, '', null, '', null, '', '', null, '', 0, '', null, null, '', '', null),
        (customer_uuid, '00000000-0000-0000-0000-000000000000', 'authenticated', 'authenticated',
         '<EMAIL>', crypt('customer123', gen_salt('bf', 10)), now(), now(), now(),
         '{"full_name": "Customer User", "role": "customer"}'::jsonb, '{"provider": "email", "providers": ["email"]}'::jsonb,
         false, false, '', null, '', null, '', '', null, '', 0, '', null, null, '', '', null);

    -- Create restaurant
    INSERT INTO public.restaurants (id, owner_id, name, description, address, phone, email, image_url, rating, delivery_fee, minimum_order, cuisine_type, is_featured)
    VALUES (restaurant_uuid, restaurant_owner_uuid, 'Delicious Delights', 'Fresh ingredients, amazing taste, fast delivery', '123 Food Street, Taste City', '+**********', '<EMAIL>', 'https://images.unsplash.com/photo-1414235077428-338989a2e8c0', 4.5, 2.99, 15.00, 'Italian', true);

    -- Create menu items
    INSERT INTO public.menu_items (id, restaurant_id, name, description, price, image_url, category, preparation_time_minutes, is_vegetarian)
    VALUES 
        (pizza_item_uuid, restaurant_uuid, 'Margherita Pizza', 'Classic pizza with fresh mozzarella, tomatoes, and basil', 18.99, 'https://images.unsplash.com/photo-1604382354936-07c5d9983bd3', 'Pizza', 20, true),
        (burger_item_uuid, restaurant_uuid, 'Classic Burger', 'Juicy beef patty with lettuce, tomato, cheese, and our special sauce', 12.99, 'https://images.unsplash.com/photo-1568901346375-23c9450c58cd', 'Burgers', 15, false);

    -- Create sample order
    INSERT INTO public.orders (id, customer_id, restaurant_id, order_number, subtotal, delivery_fee, tax_amount, total_amount, delivery_address, status)
    VALUES (order_uuid, customer_uuid, restaurant_uuid, public.generate_order_number(), 18.99, 2.99, 2.16, 24.14, '456 Customer Lane, Food City', 'confirmed'::public.order_status);

    -- Create order items
    INSERT INTO public.order_items (order_id, menu_item_id, quantity, unit_price, total_price)
    VALUES (order_uuid, pizza_item_uuid, 1, 18.99, 18.99);

EXCEPTION
    WHEN foreign_key_violation THEN
        RAISE NOTICE 'Foreign key error: %', SQLERRM;
    WHEN unique_violation THEN
        RAISE NOTICE 'Unique constraint error: %', SQLERRM;
    WHEN OTHERS THEN
        RAISE NOTICE 'Unexpected error: %', SQLERRM;
END $$;
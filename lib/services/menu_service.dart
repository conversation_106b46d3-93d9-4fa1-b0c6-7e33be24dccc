
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:foodflow/models/menu_item.dart';

class MenuService {
  final SupabaseClient _supabaseClient;

  MenuService(this._supabaseClient);

  Future<List<MenuItem>> getMenuItems(String restaurantId) async {
    final response = await _supabaseClient
        .from('menu_items')
        .select()
        .eq('restaurant_id', restaurantId)
        .order('name', ascending: true);

    return response.map((e) => MenuItem.fromMap(e)).toList();
  }

  Future<void> addMenuItem(MenuItem item) async {
    await _supabaseClient.from('menu_items').insert(item.toMap());
  }

  Future<void> updateMenuItem(MenuItem item) async {
    await _supabaseClient.from('menu_items').update(item.toMap()).eq('id', item.id);
  }

  Future<void> deleteMenuItem(String itemId) async {
    await _supabaseClient.from('menu_items').delete().eq('id', itemId);
  }
}

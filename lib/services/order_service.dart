import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:foodflow/models/order.dart';

class OrderService {
  final SupabaseClient _supabaseClient;

  OrderService(this._supabaseClient);

  Future<Order?> getOrderById(String orderId) async {
    final response = await _supabaseClient
        .from('orders')
        .select()
        .eq('id', orderId)
        .single();
    return Order.fromMap(response);
  }

  Stream<Order> subscribeToOrder(String orderId) {
    return _supabaseClient
        .from('orders')
        .stream(primaryKey: ['id'])
        .eq('id', orderId)
        .map((maps) => Order.fromMap(maps.first));
  }

  Stream<List<Order>> subscribeToRestaurantOrders(String restaurantId) {
    return _supabaseClient
        .from('orders')
        .stream(primaryKey: ['id'])
        .eq('restaurant_id', restaurantId)
        .order('created_at', ascending: false)
        .map((maps) => maps.map((map) => Order.fromMap(map)).toList());
  }

  Stream<List<Order>> subscribeToAvailableOrders() {
    return _supabaseClient
        .from('orders')
        .stream(primaryKey: ['id'])
        .eq('status', 'ready') // Assuming 'ready' means available for pickup
        .order('created_at', ascending: true)
        .map((maps) => maps.map((map) => Order.fromMap(map)).toList());
  }

  Stream<List<Order>> subscribeToAssignedOrders(String riderId) {
    return _supabaseClient
        .from('orders')
        .stream(primaryKey: ['id'])
        .eq('rider_id', riderId)
        .order('created_at', ascending: false)
        .map((maps) => maps.map((map) => Order.fromMap(map)).toList());
  }

  Future<void> updateOrderStatus(String orderId, String status) async {
    await _supabaseClient
        .from('orders')
        .update({'status': status}).eq('id', orderId);
  }

  Future<void> assignRiderToOrder(String orderId, String riderId) async {
    await _supabaseClient
        .from('orders')
        .update({'rider_id': riderId, 'status': 'picked_up'}).eq('id', orderId);
  }

  Future<List<Order>> getCompletedOrdersByRiderId(String riderId) async {
    final response = await _supabaseClient
        .from('orders')
        .select()
        .eq('rider_id', riderId)
        .eq('status', 'delivered')
        .order('actual_delivery_time', ascending: false);
    return response.map((e) => Order.fromMap(e)).toList();
  }
}

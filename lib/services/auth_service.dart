import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:foodflow/models/user_profile.dart';

class AuthService {
  final SupabaseClient _supabaseClient;

  AuthService(this._supabaseClient);

  // Auth state stream
  Stream<AuthState> get authStateChanges =>
      _supabaseClient.auth.onAuthStateChange;

  // Current user
  User? get currentUser => _supabaseClient.auth.currentUser;

  // Sign in with email and password
  Future<AuthResponse> signIn(String email, String password) async {
    return await _supabaseClient.auth.signInWithPassword(
      email: email,
      password: password,
    );
  }

  // Sign up with email, password, and role
  Future<AuthResponse> signUp({
    required String email,
    required String password,
    required String fullName,
    required String role,
  }) async {
    return await _supabaseClient.auth.signUp(
      email: email,
      password: password,
      data: {
        'full_name': fullName,
        'role': role,
      },
    );
  }

  // Sign out
  Future<void> signOut() async {
    await _supabaseClient.auth.signOut();
  }

  // Get user profile
  Future<UserProfile?> getProfile(String userId) async {
    try {
      final response = await _supabaseClient
          .from('user_profiles')
          .select()
          .eq('id', userId)
          .single();
      return UserProfile.fromMap(response);
    } catch (e) {
      return null;
    }
  }

  // Update user profile
  Future<UserProfile?> updateProfile(UserProfile profile) async {
    try {
      final response = await _supabaseClient
          .from('user_profiles')
          .update(profile.toMap())
          .eq('id', profile.id)
          .select()
          .single();
      return UserProfile.fromMap(response);
    } catch (e) {
      return null;
    }
  }

  // Get current user profile
  Future<Map<String, dynamic>?> getUserProfile() async {
    final user = currentUser;
    if (user == null) return null;

    try {
      final response = await _supabaseClient
          .from('user_profiles')
          .select()
          .eq('id', user.id)
          .single();
      return response;
    } catch (e) {
      return null;
    }
  }

  // Legacy methods for backward compatibility
  Future<User?> signInWithEmail(String email, String password) async {
    final response = await signIn(email, password);
    return response.user;
  }

  Future<User?> signUpWithEmail(
      String email, String password, String fullName) async {
    final response = await signUp(
      email: email,
      password: password,
      fullName: fullName,
      role: 'customer',
    );
    return response.user;
  }

  Future<UserProfile?> getUserProfileById(String userId) async {
    return await getProfile(userId);
  }

  User? getCurrentUser() {
    return currentUser;
  }
}

import 'package:supabase_flutter/supabase_flutter.dart';
import 'dart:async';
import 'dart:io';

class SupabaseService {
  static final SupabaseService _instance = SupabaseService._internal();
  late final SupabaseClient _client;
  bool _isInitialized = false;
  final Future<void> _initFuture;

  // Connection configuration
  static const Duration _connectionTimeout = Duration(seconds: 30);
  static const Duration _receiveTimeout = Duration(seconds: 60);
  static const Duration _sendTimeout = Duration(seconds: 30);
  static const int _maxRetries = 3;
  static const Duration _retryDelay = Duration(seconds: 2);

  // Singleton pattern
  factory SupabaseService() {
    return _instance;
  }

  SupabaseService._internal() : _initFuture = _initializeSupabase();

  static const String supabaseUrl =
      String.fromEnvironment('SUPABASE_URL', defaultValue: '');
  static const String supabaseAnonKey =
      String.fromEnvironment('SUPABASE_ANON_KEY', defaultValue: '');

  // Internal initialization logic
  static Future<void> _initializeSupabase() async {
    if (supabaseUrl.isEmpty || supabaseAnonKey.isEmpty) {
      throw Exception(
          'SUPABASE_URL and SUPABASE_ANON_KEY must be defined using --dart-define.');
    }

    await Supabase.initialize(url: supabaseUrl, anonKey: supabaseAnonKey);

    _instance._client = Supabase.instance.client;
    _instance._isInitialized = true;
  }

  // Client getter (async)
  Future<SupabaseClient> get client async {
    if (!_isInitialized) {
      await _initFuture;
    }
    return _client;
  }

  // Sync client getter (use only after initialization)
  SupabaseClient get syncClient {
    if (!_isInitialized) {
      throw Exception(
          'SupabaseService not initialized. Call SupabaseService() first.');
    }
    return _client;
  }

  // Connection health check
  Future<bool> checkConnection() async {
    try {
      final response = await _client
          .from('user_profiles')
          .select('id')
          .limit(1)
          .timeout(_connectionTimeout);
      return true;
    } catch (e) {
      return false;
    }
  }

  // Retry mechanism for database operations
  Future<T> retryOperation<T>(
    Future<T> Function() operation, {
    int maxRetries = _maxRetries,
    Duration retryDelay = _retryDelay,
  }) async {
    int attempts = 0;
    Exception? lastException;

    while (attempts < maxRetries) {
      try {
        return await operation().timeout(_receiveTimeout);
      } catch (e) {
        lastException = e is Exception ? e : Exception(e.toString());
        attempts++;

        if (attempts >= maxRetries) {
          break;
        }

        // Check if it's a timeout or connection error
        if (_isConnectionError(e)) {
          await Future.delayed(retryDelay * attempts);
          continue;
        } else {
          // For non-connection errors, throw immediately
          throw lastException;
        }
      }
    }

    throw Exception(
        'Operation failed after $maxRetries attempts: ${lastException?.toString()}');
  }

  // Check if error is connection-related
  bool _isConnectionError(dynamic error) {
    final errorString = error.toString().toLowerCase();
    return errorString.contains('timeout') ||
        errorString.contains('connection') ||
        errorString.contains('network') ||
        errorString.contains('socket') ||
        error is SocketException ||
        error is TimeoutException;
  }

  // Enhanced query execution with retry
  Future<List<Map<String, dynamic>>> executeQuery(
    PostgrestFilterBuilder query, {
    int maxRetries = _maxRetries,
  }) async {
    return await retryOperation(() async {
      final response = await query;
      return List<Map<String, dynamic>>.from(response);
    }, maxRetries: maxRetries);
  }

  // Enhanced single query execution with retry
  Future<Map<String, dynamic>> executeSingleQuery(
    PostgrestFilterBuilder query, {
    int maxRetries = _maxRetries,
  }) async {
    return await retryOperation(() async {
      final response = await query.single();
      return response;
    }, maxRetries: maxRetries);
  }

  // Enhanced insert operation with retry
  Future<Map<String, dynamic>> executeInsert(
    PostgrestBuilder query, {
    int maxRetries = _maxRetries,
  }) async {
    return await retryOperation(() async {
      final response = await query;
      return response as Map<String, dynamic>;
    }, maxRetries: maxRetries);
  }

  // Enhanced update operation with retry
  Future<Map<String, dynamic>> executeUpdate(
    PostgrestFilterBuilder query, {
    int maxRetries = _maxRetries,
  }) async {
    return await retryOperation(() async {
      final response = await query.select().single();
      return response;
    }, maxRetries: maxRetries);
  }

  // Enhanced delete operation with retry
  Future<void> executeDelete(
    PostgrestFilterBuilder query, {
    int maxRetries = _maxRetries,
  }) async {
    return await retryOperation(() async {
      await query;
    }, maxRetries: maxRetries);
  }
}

import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:foodflow/models/restaurant.dart';

final restaurantServiceProvider = Provider<RestaurantService>((ref) {
  return RestaurantService(Supabase.instance.client);
});

class RestaurantService {
  final SupabaseClient _supabaseClient;

  RestaurantService(this._supabaseClient);

  Future<List<Restaurant>> getRestaurants() async {
    final response = await _supabaseClient
        .from('restaurants')
        .select()
        .order('name', ascending: true);

    return response.map((e) => Restaurant.fromMap(e)).toList();
  }

  Future<Restaurant?> getRestaurantByOwnerId(String ownerId) async {
    final response = await _supabaseClient
        .from('restaurants')
        .select()
        .eq('owner_id', ownerId)
        .single();
    return Restaurant.fromMap(response);
  }

  Future<void> addRestaurant(Restaurant restaurant) async {
    await _supabaseClient.from('restaurants').insert(restaurant.toMap());
  }

  Future<void> updateRestaurant(Restaurant restaurant) async {
    await _supabaseClient.from('restaurants').update(restaurant.toMap()).eq('id', restaurant.id);
  }

  Future<void> deleteRestaurant(String restaurantId) async {
    await _supabaseClient.from('restaurants').delete().eq('id', restaurantId);
  }
}

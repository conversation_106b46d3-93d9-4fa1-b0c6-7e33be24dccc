import 'dart:convert';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:foodflow/core/app_export.dart';
import 'package:foodflow/routes/app_router.dart';
import 'package:foodflow/services/supabase_service.dart';
import 'package:foodflow/theme/app_theme.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Load environment variables
  final env = jsonDecode(await rootBundle.loadString('env.json'));

  // Initialize Supabase
  await Supabase.initialize(
    url: env['SUPABASE_URL'],
    anonKey: env['SUPABASE_ANON_KEY'],
  );

  // TODO: Initialize push notifications (FCM or Supabase Edge Functions)

  // Set preferred orientations
  await SystemChrome.setPreferredOrientations([
    DeviceOrientation.portraitUp,
  ]);

  runApp(const ProviderScope(child: MyApp()));
}

class MyApp extends ConsumerWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final router = ref.watch(routerProvider);

    return MaterialApp.router(
      title: 'FoodFlow',
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.system, // Or make this configurable
      debugShowCheckedModeBanner: false,
      routerConfig: router,
    );
  }
}
import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import './menu_item_card_widget.dart';

class MenuCategorySectionWidget extends StatelessWidget {
  final Map<String, dynamic> category;
  final bool isMultiSelectMode;
  final List<String> selectedItems;
  final VoidCallback onToggleExpansion;
  final Function(Map<String, dynamic>) onItemTap;
  final Function(String) onItemLongPress;
  final Function(String) onToggleAvailability;
  final Function(Map<String, dynamic>) onEditItem;
  final Function(Map<String, dynamic>) onDuplicateItem;
  final Function(String) onArchiveItem;
  final Function(String) onDeleteItem;

  const MenuCategorySectionWidget({
    super.key,
    required this.category,
    required this.isMultiSelectMode,
    required this.selectedItems,
    required this.onToggleExpansion,
    required this.onItemTap,
    required this.onItemLongPress,
    required this.onToggleAvailability,
    required this.onEditItem,
    required this.onDuplicateItem,
    required this.onArchiveItem,
    required this.onDeleteItem,
  });

  @override
  Widget build(BuildContext context) {
    final items = category["items"] as List<Map<String, dynamic>>;
    final isExpanded = category["isExpanded"] as bool;
    final itemCount = category["itemCount"] as int;

    return Container(
      margin: EdgeInsets.only(bottom: 2.h),
      decoration: BoxDecoration(
        color: AppTheme.lightTheme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppTheme.lightTheme.colorScheme.outline.withValues(alpha: 0.2),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          // Category Header
          InkWell(
            onTap: onToggleExpansion,
            borderRadius: BorderRadius.circular(12),
            child: Container(
              padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
              child: Row(
                children: [
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          category["name"] as String,
                          style: AppTheme.lightTheme.textTheme.titleLarge
                              ?.copyWith(
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        SizedBox(height: 0.5.h),
                        Text(
                          '$itemCount ${itemCount == 1 ? 'item' : 'items'}',
                          style: AppTheme.lightTheme.textTheme.bodyMedium
                              ?.copyWith(
                            color: AppTheme
                                .lightTheme.colorScheme.onSurfaceVariant,
                          ),
                        ),
                      ],
                    ),
                  ),
                  AnimatedRotation(
                    turns: isExpanded ? 0.5 : 0,
                    duration: const Duration(milliseconds: 200),
                    child: CustomIconWidget(
                      iconName: 'keyboard_arrow_down',
                      color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                      size: 24,
                    ),
                  ),
                ],
              ),
            ),
          ),

          // Category Items
          AnimatedContainer(
            duration: const Duration(milliseconds: 300),
            height: isExpanded ? null : 0,
            child: isExpanded
                ? Column(
                    children: [
                      if (items.isEmpty)
                        Container(
                          padding: EdgeInsets.all(4.w),
                          child: Column(
                            children: [
                              CustomIconWidget(
                                iconName: 'restaurant',
                                color: AppTheme
                                    .lightTheme.colorScheme.onSurfaceVariant,
                                size: 32,
                              ),
                              SizedBox(height: 1.h),
                              Text(
                                'No items in this category',
                                style: AppTheme.lightTheme.textTheme.bodyMedium
                                    ?.copyWith(
                                  color: AppTheme
                                      .lightTheme.colorScheme.onSurfaceVariant,
                                ),
                              ),
                              SizedBox(height: 0.5.h),
                              TextButton.icon(
                                onPressed: () {
                                  // Add item to this category
                                },
                                icon: CustomIconWidget(
                                  iconName: 'add',
                                  color:
                                      AppTheme.lightTheme.colorScheme.primary,
                                  size: 16,
                                ),
                                label: const Text('Add First Item'),
                              ),
                            ],
                          ),
                        )
                      else
                        ...items.map((item) => MenuItemCardWidget(
                              item: item,
                              isSelected: selectedItems.contains(item["id"]),
                              isMultiSelectMode: isMultiSelectMode,
                              onTap: () => onItemTap(item),
                              onLongPress: () => onItemLongPress(item["id"]),
                              onToggleAvailability: () =>
                                  onToggleAvailability(item["id"]),
                              onEdit: () => onEditItem(item),
                              onDuplicate: () => onDuplicateItem(item),
                              onArchive: () => onArchiveItem(item["id"]),
                              onDelete: () => onDeleteItem(item["id"]),
                            )),
                    ],
                  )
                : const SizedBox.shrink(),
          ),
        ],
      ),
    );
  }
}

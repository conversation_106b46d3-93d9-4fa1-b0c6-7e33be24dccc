import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:foodflow/features/restaurant/providers/menu_management_provider.dart';
import 'package:foodflow/models/menu_item.dart';
import 'package:foodflow/widgets/custom_error_widget.dart';
import 'package:shimmer/shimmer.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class MenuManagementScreen extends ConsumerWidget {
  const MenuManagementScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final menuItemsAsync = ref.watch(menuManagementProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Menu Management'),
        actions: [
          IconButton(
            icon: const Icon(Icons.add),
            onPressed: () {
              _showMenuItemDialog(context, ref);
            },
          ),
        ],
      ),
      body: menuItemsAsync.when(
        data: (menuItems) {
          if (menuItems.isEmpty) {
            return const Center(child: Text('No menu items found.'));
          }
          return ListView.builder(
            itemCount: menuItems.length,
            itemBuilder: (context, index) {
              final item = menuItems[index];
              return ListTile(
                leading: Image.network(item.imageUrl),
                title: Text(item.name),
                subtitle: Text('${item.description} - ${item.price.toStringAsFixed(2)}'),
                trailing: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    IconButton(
                      icon: const Icon(Icons.edit),
                      onPressed: () {
                        _showMenuItemDialog(context, ref, menuItem: item);
                      },
                    ),
                    IconButton(
                      icon: const Icon(Icons.delete),
                      onPressed: () {
                        ref.read(menuManagementProvider.notifier).deleteMenuItem(item.id);
                      },
                    ),
                  ],
                ),
              );
            },
          );
        },
        loading: () => Shimmer.fromColors(
          baseColor: Colors.grey[300]!,
          highlightColor: Colors.grey[100]!,
          child: ListView.builder(
            itemCount: 5,
            itemBuilder: (context, index) => ListTile(
              leading: Container(
                width: 50,
                height: 50,
                color: Colors.white,
              ),
              title: Container(
                height: 20,
                color: Colors.white,
              ),
              subtitle: Container(
                height: 15,
                color: Colors.white,
              ),
            ),
          ),
        ),
        error: (error, stackTrace) => CustomErrorWidget(error.toString()),
      ),
    );
  }

  void _showMenuItemDialog(BuildContext context, WidgetRef ref, {MenuItem? menuItem}) {
    final isEditing = menuItem != null;
    final nameController = TextEditingController(text: menuItem?.name);
    final descriptionController = TextEditingController(text: menuItem?.description);
    final priceController = TextEditingController(text: menuItem?.price.toString());
    final imageUrlController = TextEditingController(text: menuItem?.imageUrl);
    final categoryController = TextEditingController(text: menuItem?.category);
    bool isAvailable = menuItem?.isAvailable ?? true;

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(isEditing ? 'Edit Menu Item' : 'Add Menu Item'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              TextField(
                controller: nameController,
                decoration: const InputDecoration(labelText: 'Name'),
              ),
              TextField(
                controller: descriptionController,
                decoration: const InputDecoration(labelText: 'Description'),
              ),
              TextField(
                controller: priceController,
                decoration: const InputDecoration(labelText: 'Price'),
                keyboardType: TextInputType.number,
              ),
              TextField(
                controller: imageUrlController,
                decoration: const InputDecoration(labelText: 'Image URL'),
              ),
              TextField(
                controller: categoryController,
                decoration: const InputDecoration(labelText: 'Category'),
              ),
              StatefulBuilder(
                builder: (context, setState) {
                  return CheckboxListTile(
                    title: const Text('Available'),
                    value: isAvailable,
                    onChanged: (bool? value) {
                      setState(() {
                        isAvailable = value!;
                      });
                    },
                  );
                },
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              final newMenuItem = MenuItem(
                id: menuItem?.id ?? '',
                restaurantId: menuItem?.restaurantId ?? '', // This needs to be fetched dynamically
                name: nameController.text,
                description: descriptionController.text,
                price: double.parse(priceController.text),
                imageUrl: imageUrlController.text,
                category: categoryController.text,
                isAvailable: isAvailable,
              );

              if (isEditing) {
                await ref.read(menuManagementProvider.notifier).updateMenuItem(newMenuItem);
              } else {
                // Fetch restaurantId for new item
                final userId = Supabase.instance.client.auth.currentUser?.id;
                if (userId != null) {
                  final restaurantResponse = await Supabase.instance.client
                      .from('restaurants')
                      .select('id')
                      .eq('owner_id', userId)
                      .single();
                  final restaurantId = restaurantResponse['id'];
                  final itemToAdd = MenuItem(
                    id: newMenuItem.id,
                    restaurantId: restaurantId,
                    name: newMenuItem.name,
                    description: newMenuItem.description,
                    price: newMenuItem.price,
                    imageUrl: newMenuItem.imageUrl,
                    category: newMenuItem.category,
                    isAvailable: newMenuItem.isAvailable,
                  );
                  await ref.read(menuManagementProvider.notifier).addMenuItem(itemToAdd);
                }
              }
              Navigator.pop(context);
            },
            child: Text(isEditing ? 'Save' : 'Add'),
          ),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sizer/sizer.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../core/app_export.dart';

class PromoCodeWidget extends StatefulWidget {
  final Function(String code, double discount) onPromoApplied;

  const PromoCodeWidget({
    super.key,
    required this.onPromoApplied,
  });

  @override
  State<PromoCodeWidget> createState() => _PromoCodeWidgetState();
}

class _PromoCodeWidgetState extends State<PromoCodeWidget> {
  final TextEditingController _promoController = TextEditingController();
  bool _isApplying = false;
  String? _appliedCode;
  double _discount = 0.0;
  String _errorMessage = '';

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.local_offer_outlined,
                size: 6.w,
                color: Theme.of(context).primaryColor,
              ),
              SizedBox(width: 2.w),
              Text(
                'Promo Code',
                style: GoogleFonts.inter(
                  fontSize: 16.sp,
                  fontWeight: FontWeight.w600,
                  color: Theme.of(context).colorScheme.onSurface,
                ),
              ),
            ],
          ),

          SizedBox(height: 2.h),

          if (_appliedCode != null) ...[
            // Applied promo code display
            Container(
              width: double.infinity,
              padding: EdgeInsets.all(3.w),
              decoration: BoxDecoration(
                color: Colors.green.withAlpha(26),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: Colors.green.withAlpha(77),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.check_circle,
                    size: 5.w,
                    color: Colors.green,
                  ),
                  SizedBox(width: 2.w),
                  Expanded(
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        Text(
                          'Code Applied: $_appliedCode',
                          style: GoogleFonts.inter(
                            fontSize: 14.sp,
                            fontWeight: FontWeight.w600,
                            color: Colors.green,
                          ),
                        ),
                        Text(
                          'Discount: \${_discount.toStringAsFixed(2)}',
                          style: GoogleFonts.inter(
                            fontSize: 12.sp,
                            fontWeight: FontWeight.w400,
                            color: Colors.green,
                          ),
                        ),
                      ],
                    ),
                  ),
                  TextButton(
                    onPressed: _removePromoCode,
                    child: Text(
                      'Remove',
                      style: GoogleFonts.inter(
                        fontSize: 12.sp,
                        fontWeight: FontWeight.w600,
                        color: Colors.red,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ] else ...[
            // Promo code input
            Row(
              children: [
                Expanded(
                  child: TextFormField(
                    controller: _promoController,
                    decoration: InputDecoration(
                      hintText: 'Enter promo code',
                      hintStyle: GoogleFonts.inter(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w400,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                      filled: true,
                      fillColor: Theme.of(context)
                          .colorScheme
                          .surfaceContainerHighest
                          .withAlpha(77),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                        borderSide: BorderSide.none,
                      ),
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: 3.w,
                        vertical: 2.h,
                      ),
                      errorText:
                          _errorMessage.isNotEmpty ? _errorMessage : null,
                    ),
                    style: GoogleFonts.inter(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w500,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                    textCapitalization: TextCapitalization.characters,
                    onChanged: (value) {
                      if (_errorMessage.isNotEmpty) {
                        setState(() {
                          _errorMessage = '';
                        });
                      }
                    },
                  ),
                ),
                SizedBox(width: 2.w),
                ElevatedButton(
                  onPressed: _isApplying ? null : _applyPromoCode,
                  style: ElevatedButton.styleFrom(
                    backgroundColor: Theme.of(context).primaryColor,
                    foregroundColor: Colors.white,
                    padding: EdgeInsets.symmetric(
                      horizontal: 6.w,
                      vertical: 2.h,
                    ),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                  child: _isApplying
                      ? SizedBox(
                          width: 4.w,
                          height: 4.w,
                          child: const CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor:
                                AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : Text(
                          'Apply',
                          style: GoogleFonts.inter(
                            fontSize: 14.sp,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                ),
              ],
            ),
          ],

          SizedBox(height: 2.h),

          // Available promo codes
          Text(
            'Available Codes:',
            style: GoogleFonts.inter(
              fontSize: 12.sp,
              fontWeight: FontWeight.w600,
              color: Theme.of(context).colorScheme.onSurfaceVariant,
            ),
          ),

          SizedBox(height: 1.h),

          Wrap(
            spacing: 2.w,
            children: [
              _buildPromoChip(context, 'WELCOME10', '10% off'),
              _buildPromoChip(context, 'SAVE5', '\$5 off'),
              _buildPromoChip(context, 'FREEDEL', 'Free delivery'),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildPromoChip(
      BuildContext context, String code, String description) {
    return InkWell(
      onTap: () {
        _promoController.text = code;
        _applyPromoCode();
      },
      child: Container(
        padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.h),
        decoration: BoxDecoration(
          color: Theme.of(context).primaryColor.withAlpha(26),
          borderRadius: BorderRadius.circular(20),
          border: Border.all(
            color: Theme.of(context).primaryColor.withAlpha(77),
          ),
        ),
        child: Text(
          '$code - $description',
          style: GoogleFonts.inter(
            fontSize: 10.sp,
            fontWeight: FontWeight.w500,
            color: Theme.of(context).primaryColor,
          ),
        ),
      ),
    );
  }

  Future<void> _applyPromoCode() async {
    final code = _promoController.text.trim().toUpperCase();

    if (code.isEmpty) {
      setState(() {
        _errorMessage = 'Please enter a promo code';
      });
      return;
    }

    setState(() {
      _isApplying = true;
      _errorMessage = '';
    });

    // Simulate API call
    await Future.delayed(const Duration(seconds: 1));

    // Mock promo code validation
    double discount = 0.0;
    bool isValid = true;

    switch (code) {
      case 'WELCOME10':
        discount = 5.0; // 10% of a mock subtotal of $50
        break;
      case 'SAVE5':
        discount = 5.0;
        break;
      case 'FREEDEL':
        discount = 2.99; // Mock delivery fee
        break;
      default:
        isValid = false;
        break;
    }

    setState(() {
      _isApplying = false;
      if (isValid) {
        _appliedCode = code;
        _discount = discount;
        _errorMessage = '';
        _promoController.clear();
        widget.onPromoApplied(code, discount);
        HapticFeedback.lightImpact();
      } else {
        _errorMessage = 'Invalid promo code';
      }
    });
  }

  void _removePromoCode() {
    setState(() {
      _appliedCode = null;
      _discount = 0.0;
      _errorMessage = '';
    });
    widget.onPromoApplied('', 0.0);
    HapticFeedback.lightImpact();
  }

  @override
  void dispose() {
    _promoController.dispose();
    super.dispose();
  }
}
import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../core/app_export.dart';

class OrderSummaryWidget extends StatelessWidget {
  final double subtotal;
  final double deliveryFee;
  final double taxAmount;
  final double promoDiscount;
  final double total;

  const OrderSummaryWidget({
    super.key,
    required this.subtotal,
    required this.deliveryFee,
    required this.taxAmount,
    required this.promoDiscount,
    required this.total,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Order Summary',
            style: GoogleFonts.inter(
              fontSize: 18.sp,
              fontWeight: FontWeight.w600,
              color: Theme.of(context).colorScheme.onSurface,
            ),
          ),
          SizedBox(height: 2.h),
          _buildSummaryRow(
            context,
            'Subtotal',
            '\${subtotal.toStringAsFixed(2)}',
          ),
          SizedBox(height: 1.h),
          _buildSummaryRow(
            context,
            'Delivery Fee',
            '\${deliveryFee.toStringAsFixed(2)}',
          ),
          SizedBox(height: 1.h),
          _buildSummaryRow(
            context,
            'Tax',
            '\${taxAmount.toStringAsFixed(2)}',
          ),
          if (promoDiscount > 0) ...[
            SizedBox(height: 1.h),
            _buildSummaryRow(
              context,
              'Promo Discount',
              '-\${promoDiscount.toStringAsFixed(2)}',
              isDiscount: true,
            ),
          ],
          Padding(
            padding: EdgeInsets.symmetric(vertical: 1.5.h),
            child: Divider(
              color: Theme.of(context).colorScheme.outline.withAlpha(77),
            ),
          ),
          _buildSummaryRow(
            context,
            'Total',
            '\${total.toStringAsFixed(2)}',
            isTotal: true,
          ),
        ],
      ),
    );
  }

  Widget _buildSummaryRow(
    BuildContext context,
    String label,
    String value, {
    bool isTotal = false,
    bool isDiscount = false,
  }) {
    return Row(
      mainAxisAlignment: MainAxisAlignment.spaceBetween,
      children: [
        Text(
          label,
          style: GoogleFonts.inter(
            fontSize: isTotal ? 16.sp : 14.sp,
            fontWeight: isTotal ? FontWeight.w600 : FontWeight.w400,
            color: Theme.of(context).colorScheme.onSurface,
          ),
        ),
        Text(
          value,
          style: GoogleFonts.inter(
            fontSize: isTotal ? 16.sp : 14.sp,
            fontWeight: isTotal ? FontWeight.w700 : FontWeight.w500,
            color: isDiscount
                ? Colors.green
                : isTotal
                    ? Theme.of(context).primaryColor
                    : Theme.of(context).colorScheme.onSurface,
          ),
        ),
      ],
    );
  }
}
import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../core/app_export.dart';

class OrderTrackingHeaderWidget extends StatefulWidget {
  final Function(String) onSearchChanged;

  const OrderTrackingHeaderWidget({
    super.key,
    required this.onSearchChanged,
  });

  @override
  State<OrderTrackingHeaderWidget> createState() =>
      _OrderTrackingHeaderWidgetState();
}

class _OrderTrackingHeaderWidgetState extends State<OrderTrackingHeaderWidget> {
  final TextEditingController _searchController = TextEditingController();
  bool _isSearchExpanded = false;

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: EdgeInsets.only(
        top: MediaQuery.of(context).padding.top + 1.h,
        left: 4.w,
        right: 4.w,
        bottom: 2.h,
      ),
      decoration: BoxDecoration(
        color: Theme.of(context).scaffoldBackgroundColor,
        boxShadow: [
          BoxShadow(
            color: Colors.black.withAlpha(13),
            blurRadius: 10,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        children: [
          // Top row with title and search
          Row(
            children: [
              if (!_isSearchExpanded) ...[
                Expanded(
                  child: Text(
                    'Orders',
                    style: GoogleFonts.inter(
                      fontSize: 24.sp,
                      fontWeight: FontWeight.w700,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                  ),
                ),
                IconButton(
                  onPressed: _toggleSearch,
                  icon: Icon(
                    Icons.search,
                    size: 6.w,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
              ] else ...[
                Expanded(
                  child: TextField(
                    controller: _searchController,
                    decoration: InputDecoration(
                      hintText: 'Search orders...',
                      hintStyle: GoogleFonts.inter(
                        fontSize: 14.sp,
                        fontWeight: FontWeight.w400,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                      filled: true,
                      fillColor: Theme.of(context)
                          .colorScheme
                          .surfaceContainerHighest
                          .withAlpha(77),
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(12),
                        borderSide: BorderSide.none,
                      ),
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: 4.w,
                        vertical: 1.5.h,
                      ),
                      prefixIcon: Icon(
                        Icons.search,
                        size: 5.w,
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                    ),
                    style: GoogleFonts.inter(
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w500,
                      color: Theme.of(context).colorScheme.onSurface,
                    ),
                    onChanged: widget.onSearchChanged,
                    autofocus: true,
                  ),
                ),
                SizedBox(width: 2.w),
                IconButton(
                  onPressed: _toggleSearch,
                  icon: Icon(
                    Icons.close,
                    size: 6.w,
                    color: Theme.of(context).colorScheme.onSurface,
                  ),
                ),
              ],
            ],
          ),
        ],
      ),
    );
  }

  void _toggleSearch() {
    setState(() {
      _isSearchExpanded = !_isSearchExpanded;
      if (!_isSearchExpanded) {
        _searchController.clear();
        widget.onSearchChanged('');
      }
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }
}
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:foodflow/models/order.dart';
import 'package:foodflow/services/order_service.dart';
import 'package:foodflow/widgets/custom_error_widget.dart';
import 'package:shimmer/shimmer.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

final orderServiceProvider = Provider<OrderService>((ref) {
  return OrderService(Supabase.instance.client);
});

final orderProvider = FutureProvider.family<Order?, String>((ref, orderId) async {
  final orderService = ref.watch(orderServiceProvider);
  return await orderService.getOrderById(orderId);
});

final orderStreamProvider = StreamProvider.family<Order, String>((ref, orderId) {
  final orderService = ref.watch(orderServiceProvider);
  return orderService.subscribeToOrder(orderId);
});

class OrderTrackingScreen extends ConsumerWidget {
  final String? orderId;

  const OrderTrackingScreen({super.key, this.orderId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    if (orderId == null) {
      return const Center(child: Text('No order selected.'));
    }

    final orderAsync = ref.watch(orderStreamProvider(orderId!));

    return Scaffold(
      appBar: AppBar(
        title: const Text('Track Your Order'),
      ),
      body: orderAsync.when(
        data: (order) {
          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Order Number: ${order.orderNumber}', style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                const SizedBox(height: 8),
                Text('Status: ${order.status}'),
                const SizedBox(height: 8),
                Text('Total Amount: ${order.totalAmount.toStringAsFixed(2)}'),
                const SizedBox(height: 8),
                Text('Delivery Address: ${order.deliveryAddress}'),
                const SizedBox(height: 16),
                // TODO: Add map view for rider tracking
                // TODO: Add timeline for order status
              ],
            ),
          );
        },
        loading: () => Shimmer.fromColors(
          baseColor: Colors.grey[300]!,
          highlightColor: Colors.grey[100]!,
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(height: 20, width: 150, color: Colors.white),
                const SizedBox(height: 8),
                Container(height: 15, width: 100, color: Colors.white),
                const SizedBox(height: 8),
                Container(height: 15, width: 120, color: Colors.white),
                const SizedBox(height: 8),
                Container(height: 15, width: 200, color: Colors.white),
              ],
            ),
          ),
        ),
        error: (error, stackTrace) => CustomErrorWidget(error.toString()),
      ),
    );
  }
}
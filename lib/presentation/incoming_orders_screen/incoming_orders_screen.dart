import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:foodflow/providers/service_providers.dart';
import 'package:foodflow/models/order.dart';
import 'package:foodflow/widgets/custom_error_widget.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class IncomingOrdersScreen extends ConsumerWidget {
  const IncomingOrdersScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final orderService = ref.watch(orderServiceProvider);
    final currentUser = Supabase.instance.client.auth.currentUser;

    if (currentUser == null) {
      return const Scaffold(
        body: Center(
          child: Text('Please log in to view orders'),
        ),
      );
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Incoming Orders'),
        backgroundColor: Theme.of(context).colorScheme.primary,
        foregroundColor: Colors.white,
      ),
      body: StreamBuilder<List<Order>>(
        stream: _getRestaurantOrdersStream(orderService, currentUser.id),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            return const Center(child: CircularProgressIndicator());
          }

          if (snapshot.hasError) {
            return CustomErrorWidget(snapshot.error.toString());
          }

          final orders = snapshot.data ?? [];

          if (orders.isEmpty) {
            return const Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(Icons.inbox, size: 64, color: Colors.grey),
                  SizedBox(height: 16),
                  Text(
                    'No incoming orders',
                    style: TextStyle(fontSize: 18, color: Colors.grey),
                  ),
                ],
              ),
            );
          }

          return ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: orders.length,
            itemBuilder: (context, index) {
              final order = orders[index];
              return Card(
                margin: const EdgeInsets.only(bottom: 16),
                child: Padding(
                  padding: const EdgeInsets.all(16),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceBetween,
                        children: [
                          Text(
                            'Order #${order.id.substring(0, 8)}',
                            style: const TextStyle(
                              fontWeight: FontWeight.bold,
                              fontSize: 16,
                            ),
                          ),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: _getStatusColor(order.status),
                              borderRadius: BorderRadius.circular(12),
                            ),
                            child: Text(
                              order.status.toUpperCase(),
                              style: const TextStyle(
                                color: Colors.white,
                                fontSize: 12,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text('Total: \$${order.totalAmount.toStringAsFixed(2)}'),
                      Text('Delivery Address: ${order.deliveryAddress}'),
                      const SizedBox(height: 16),
                      Row(
                        children: [
                          if (order.status == 'pending')
                            Expanded(
                              child: ElevatedButton.icon(
                                onPressed: () => _acceptOrder(ref, order.id),
                                icon: const Icon(Icons.check,
                                    color: Colors.white),
                                label: const Text('Accept'),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.green,
                                  foregroundColor: Colors.white,
                                ),
                              ),
                            ),
                          if (order.status == 'confirmed')
                            Expanded(
                              child: ElevatedButton.icon(
                                onPressed: () =>
                                    _markAsPreparing(ref, order.id),
                                icon: const Icon(Icons.kitchen,
                                    color: Colors.white),
                                label: const Text('Start Preparing'),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.blue,
                                  foregroundColor: Colors.white,
                                ),
                              ),
                            ),
                          if (order.status == 'preparing')
                            Expanded(
                              child: ElevatedButton.icon(
                                onPressed: () => _markAsReady(ref, order.id),
                                icon:
                                    const Icon(Icons.done, color: Colors.white),
                                label: const Text('Mark Ready'),
                                style: ElevatedButton.styleFrom(
                                  backgroundColor: Colors.orange,
                                  foregroundColor: Colors.white,
                                ),
                              ),
                            ),
                        ],
                      ),
                    ],
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }

  Stream<List<Order>> _getRestaurantOrdersStream(
    orderService,
    String userId,
  ) async* {
    try {
      // Get restaurant ID for the current user
      final restaurantResponse = await Supabase.instance.client
          .from('restaurants')
          .select('id')
          .eq('owner_id', userId)
          .single();

      final restaurantId = restaurantResponse['id'];
      yield* orderService.subscribeToRestaurantOrders(restaurantId);
    } catch (e) {
      yield [];
    }
  }

  Color _getStatusColor(String status) {
    switch (status.toLowerCase()) {
      case 'pending':
        return Colors.orange;
      case 'confirmed':
        return Colors.blue;
      case 'preparing':
        return Colors.purple;
      case 'ready':
        return Colors.green;
      default:
        return Colors.grey;
    }
  }

  Future<void> _acceptOrder(WidgetRef ref, String orderId) async {
    try {
      await ref
          .read(orderServiceProvider)
          .updateOrderStatus(orderId, 'confirmed');
    } catch (e) {
      // Handle error
    }
  }

  Future<void> _markAsPreparing(WidgetRef ref, String orderId) async {
    try {
      await ref
          .read(orderServiceProvider)
          .updateOrderStatus(orderId, 'preparing');
    } catch (e) {
      // Handle error
    }
  }

  Future<void> _markAsReady(WidgetRef ref, String orderId) async {
    try {
      await ref.read(orderServiceProvider).updateOrderStatus(orderId, 'ready');
    } catch (e) {
      // Handle error
    }
  }
}

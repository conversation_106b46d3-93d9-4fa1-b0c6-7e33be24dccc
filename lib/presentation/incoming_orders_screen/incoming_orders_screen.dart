import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:foodflow/features/restaurant/providers/restaurant_order_provider.dart';
import 'package:foodflow/widgets/custom_error_widget.dart';
import 'package:shimmer/shimmer.dart';

class IncomingOrdersScreen extends ConsumerWidget {
  const IncomingOrdersScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final ordersAsync = ref.watch(restaurantOrdersProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Incoming Orders'),
      ),
      body: ordersAsync.when(
        data: (orders) {
          if (orders.isEmpty) {
            return const Center(child: Text('No incoming orders.'));
          }
          return ListView.builder(
            itemCount: orders.length,
            itemBuilder: (context, index) {
              final order = orders[index];
              return ListTile(
                title: Text('Order #${order.orderNumber}'),
                subtitle: Text('Status: ${order.status} - Total: \$${order.totalAmount.toStringAsFixed(2)}'),
                trailing: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    if (order.status == 'pending')
                      IconButton(
                        icon: const Icon(Icons.check, color: Colors.green),
                        onPressed: () {
                          // TODO: Implement accept order
                        },
                      ),
                    if (order.status == 'confirmed')
                      IconButton(
                        icon: const Icon(Icons.kitchen, color: Colors.blue),
                        onPressed: () {
                          // TODO: Implement mark as preparing
                        },
                      ),
                  ],
                ),
              );
            },
          );
        },
        loading: () => Shimmer.fromColors(
          baseColor: Colors.grey[300]!,
          highlightColor: Colors.grey[100]!,
          child: ListView.builder(
            itemCount: 5,
            itemBuilder: (context, index) => ListTile(
              title: Container(height: 20, color: Colors.white),
              subtitle: Container(height: 15, color: Colors.white),
            ),
          ),
        ),
        error: (error, stackTrace) => CustomErrorWidget(error.toString()),
      ),
    );
  }
}

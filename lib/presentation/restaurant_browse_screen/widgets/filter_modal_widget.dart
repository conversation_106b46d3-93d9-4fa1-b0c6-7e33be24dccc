import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class FilterModalWidget extends StatefulWidget {
  final List<String> activeFilters;
  final Function(List<String>) onFiltersChanged;

  const FilterModalWidget({
    super.key,
    required this.activeFilters,
    required this.onFiltersChanged,
  });

  @override
  State<FilterModalWidget> createState() => _FilterModalWidgetState();
}

class _FilterModalWidgetState extends State<FilterModalWidget> {
  late List<String> _selectedFilters;

  final Map<String, List<String>> _filterCategories = {
    'Cuisine': [
      'Italian',
      'Chinese',
      'American',
      'Indian',
      'Japanese',
      'Mexican',
      'Thai',
      'Mediterranean'
    ],
    'Price Range': ['\$', '\$\$', '\$\$\$', '\$\$\$\$'],
    'Delivery Time': ['Under 30 min', '30-45 min', '45+ min'],
    'Rating': ['4.5+ stars', '4.0+ stars', '3.5+ stars'],
    'Features': [
      'Open Now',
      'Free Delivery',
      'Accepts Vouchers',
      'New Restaurant'
    ],
  };

  @override
  void initState() {
    super.initState();
    _selectedFilters = List.from(widget.activeFilters);
  }

  void _toggleFilter(String category, String filter) {
    setState(() {
      final filterKey = '$category:$filter';
      if (_selectedFilters.contains(filterKey)) {
        _selectedFilters.remove(filterKey);
      } else {
        _selectedFilters.add(filterKey);
      }
    });
  }

  void _clearAllFilters() {
    setState(() {
      _selectedFilters.clear();
    });
  }

  void _applyFilters() {
    widget.onFiltersChanged(_selectedFilters);
    Navigator.pop(context);
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 90.h,
      decoration: BoxDecoration(
        color: AppTheme.lightTheme.colorScheme.surface,
        borderRadius: const BorderRadius.vertical(top: Radius.circular(20)),
      ),
      child: Column(
        children: [
          // Handle
          Container(
            width: 40,
            height: 4,
            margin: const EdgeInsets.symmetric(vertical: 12),
            decoration: BoxDecoration(
              color: AppTheme.lightTheme.colorScheme.outline,
              borderRadius: BorderRadius.circular(2),
            ),
          ),

          // Header
          Padding(
            padding: EdgeInsets.symmetric(horizontal: 4.w),
            child: Row(
              children: [
                Text(
                  'Filters',
                  style: AppTheme.lightTheme.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: _clearAllFilters,
                  child: const Text('Clear All'),
                ),
                IconButton(
                  onPressed: () => Navigator.pop(context),
                  icon: CustomIconWidget(
                    iconName: 'close',
                    color: AppTheme.lightTheme.colorScheme.onSurface,
                    size: 24,
                  ),
                ),
              ],
            ),
          ),

          const Divider(),

          // Filter Categories
          Expanded(
            child: ListView.builder(
              padding: EdgeInsets.symmetric(horizontal: 4.w),
              itemCount: _filterCategories.keys.length,
              itemBuilder: (context, index) {
                final category = _filterCategories.keys.elementAt(index);
                final filters = _filterCategories[category]!;

                return ExpansionTile(
                  title: Text(
                    category,
                    style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  initiallyExpanded: true,
                  children: [
                    Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 16),
                      child: Wrap(
                        spacing: 8,
                        runSpacing: 8,
                        children: filters.map((filter) {
                          final filterKey = category == 'Features'
                              ? filter
                              : '$category:$filter';
                          final isSelected =
                              _selectedFilters.contains(filterKey);

                          return GestureDetector(
                            onTap: () => category == 'Features'
                                ? setState(() {
                                    if (_selectedFilters.contains(filter)) {
                                      _selectedFilters.remove(filter);
                                    } else {
                                      _selectedFilters.add(filter);
                                    }
                                  })
                                : _toggleFilter(category, filter),
                            child: Container(
                              padding: const EdgeInsets.symmetric(
                                  horizontal: 16, vertical: 8),
                              decoration: BoxDecoration(
                                color: isSelected
                                    ? AppTheme.lightTheme.colorScheme.primary
                                    : AppTheme.lightTheme.colorScheme.surface,
                                borderRadius: BorderRadius.circular(20),
                                border: Border.all(
                                  color: isSelected
                                      ? AppTheme.lightTheme.colorScheme.primary
                                      : AppTheme.lightTheme.colorScheme.outline,
                                  width: 1,
                                ),
                              ),
                              child: Text(
                                filter,
                                style: AppTheme.lightTheme.textTheme.bodyMedium
                                    ?.copyWith(
                                  color: isSelected
                                      ? Colors.white
                                      : AppTheme
                                          .lightTheme.colorScheme.onSurface,
                                  fontWeight: isSelected
                                      ? FontWeight.w600
                                      : FontWeight.w400,
                                ),
                              ),
                            ),
                          );
                        }).toList(),
                      ),
                    ),
                    SizedBox(height: 2.h),
                  ],
                );
              },
            ),
          ),

          // Apply Button
          Container(
            width: double.infinity,
            padding: EdgeInsets.all(4.w),
            decoration: BoxDecoration(
              color: AppTheme.lightTheme.colorScheme.surface,
              border: Border(
                top: BorderSide(
                  color: AppTheme.lightTheme.colorScheme.outline,
                  width: 1,
                ),
              ),
            ),
            child: Row(
              children: [
                Expanded(
                  child: OutlinedButton(
                    onPressed: () => Navigator.pop(context),
                    child: const Text('Cancel'),
                  ),
                ),
                SizedBox(width: 4.w),
                Expanded(
                  child: ElevatedButton(
                    onPressed: _applyFilters,
                    child: Text(
                      'Apply${_selectedFilters.isNotEmpty ? ' (${_selectedFilters.length})' : ''}',
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

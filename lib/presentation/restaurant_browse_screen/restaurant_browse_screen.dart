
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:foodflow/features/restaurant/providers/restaurant_provider.dart';
import 'package:foodflow/routes/app_routes.dart';
import 'package:foodflow/widgets/custom_error_widget.dart';
import 'package:go_router/go_router.dart';
import 'package:shimmer/shimmer.dart';

class RestaurantBrowseScreen extends ConsumerWidget {
  const RestaurantBrowseScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final restaurantsAsync = ref.watch(restaurantProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Browse Restaurants'),
      ),
      body: restaurantsAsync.when(
        data: (restaurants) {
          if (restaurants.isEmpty) {
            return const Center(child: Text('No restaurants found.'));
          }
          return ListView.builder(
            itemCount: restaurants.length,
            itemBuilder: (context, index) {
              final restaurant = restaurants[index];
              return ListTile(
                leading: Image.network(restaurant.imageUrl!),
                title: Text(restaurant.name),
                subtitle: Text(restaurant.description!),
                onTap: () {
                  // Navigate to restaurant menu
                  context.go('${AppRoutes.restaurantMenuScreen}/${restaurant.id}');
                },
              );
            },
          );
        },
        loading: () => Shimmer.fromColors(
          baseColor: Colors.grey[300]!,
          highlightColor: Colors.grey[100]!,
          child: ListView.builder(
            itemCount: 5,
            itemBuilder: (context, index) => ListTile(
              leading: Container(
                width: 50,
                height: 50,
                color: Colors.white,
              ),
              title: Container(
                height: 20,
                color: Colors.white,
              ),
              subtitle: Container(
                height: 15,
                color: Colors.white,
              ),
            ),
          ),
        ),
        error: (error, stackTrace) => CustomErrorWidget(error.toString()),
      ),
    );
  }
}

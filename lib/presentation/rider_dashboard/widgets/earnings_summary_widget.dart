import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class EarningsSummaryWidget extends StatelessWidget {
  final Map<String, dynamic> earningsData;

  const EarningsSummaryWidget({
    super.key,
    required this.earningsData,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Today's earnings highlight
        Container(
          width: double.infinity,
          padding: EdgeInsets.all(6.w),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                AppTheme.lightTheme.colorScheme.primary,
                AppTheme.lightTheme.colorScheme.primary.withValues(alpha: 0.8),
              ],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(16),
          ),
          child: Column(
            children: [
              Text(
                'Today\'s Earnings',
                style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                  color: Colors.white,
                ),
              ),
              SizedBox(height: 1.h),
              Text(
                earningsData['todayEarnings'] as String,
                style: AppTheme.lightTheme.textTheme.displaySmall?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(height: 2.h),
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                children: [
                  _buildTodayMetric(
                    'Deliveries',
                    '${earningsData['deliveriesCompleted']}',
                    'delivery_dining',
                  ),
                  _buildTodayMetric(
                    'Online Time',
                    earningsData['onlineTime'] as String,
                    'schedule',
                  ),
                  _buildTodayMetric(
                    'Rating',
                    '${earningsData['averageRating']}⭐',
                    'star',
                  ),
                ],
              ),
            ],
          ),
        ),

        SizedBox(height: 3.h),

        // Earnings breakdown
        Row(
          children: [
            Expanded(
              child: _buildEarningsCard(
                'This Week',
                earningsData['weeklyEarnings'] as String,
                'trending_up',
                AppTheme.successLight,
              ),
            ),
            SizedBox(width: 3.w),
            Expanded(
              child: _buildEarningsCard(
                'This Month',
                earningsData['monthlyEarnings'] as String,
                'calendar_month',
                AppTheme.lightTheme.colorScheme.secondary,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildTodayMetric(String label, String value, String iconName) {
    return Column(
      children: [
        CustomIconWidget(
          iconName: iconName,
          color: Colors.white.withValues(alpha: 0.8),
          size: 20,
        ),
        SizedBox(height: 1.h),
        Text(
          value,
          style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
            color: Colors.white,
            fontWeight: FontWeight.w600,
          ),
        ),
        Text(
          label,
          style: AppTheme.lightTheme.textTheme.bodySmall?.copyWith(
            color: Colors.white.withValues(alpha: 0.8),
          ),
        ),
      ],
    );
  }

  Widget _buildEarningsCard(
      String title, String amount, String iconName, Color color) {
    return Container(
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: AppTheme.lightTheme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppTheme.lightTheme.colorScheme.outline,
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: EdgeInsets.all(2.w),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: CustomIconWidget(
                  iconName: iconName,
                  color: color,
                  size: 20,
                ),
              ),
              const Spacer(),
            ],
          ),
          SizedBox(height: 2.h),
          Text(
            title,
            style: AppTheme.lightTheme.textTheme.bodyMedium,
          ),
          SizedBox(height: 0.5.h),
          Text(
            amount,
            style: AppTheme.lightTheme.textTheme.headlineSmall?.copyWith(
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../theme/app_theme.dart';

class RiderStatusWidget extends StatelessWidget {
  final bool isOnline;
  final VoidCallback onToggle;

  const RiderStatusWidget({
    super.key,
    required this.isOnline,
    required this.onToggle,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: EdgeInsets.all(4.w),
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: AppTheme.lightTheme.colorScheme.surface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppTheme.lightTheme.colorScheme.outline,
        ),
      ),
      child: Row(
        children: [
          // Status indicator
          Container(
            width: 3.w,
            height: 3.w,
            decoration: BoxDecoration(
              color: isOnline
                  ? AppTheme.successLight
                  : AppTheme.lightTheme.colorScheme.outline,
              shape: BoxShape.circle,
            ),
          ),

          SizedBox(width: 3.w),

          // Status text
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  isOnline ? 'Online' : 'Offline',
                  style: AppTheme.lightTheme.textTheme.titleMedium?.copyWith(
                    color: isOnline
                        ? AppTheme.successLight
                        : AppTheme.lightTheme.colorScheme.onSurfaceVariant,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  isOnline
                      ? 'You\'re receiving delivery requests'
                      : 'Go online to start receiving requests',
                  style: AppTheme.lightTheme.textTheme.bodySmall,
                ),
              ],
            ),
          ),

          // Toggle switch
          Switch(
            value: isOnline,
            onChanged: (_) => onToggle(),
            activeColor: AppTheme.successLight,
          ),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:foodflow/features/customer/providers/cart_provider.dart';
import 'package:foodflow/models/menu_item.dart';
import 'package:foodflow/services/menu_service.dart';
import 'package:foodflow/widgets/custom_error_widget.dart';
import 'package:shimmer/shimmer.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

final menuItemsProvider = FutureProvider.family<List<MenuItem>, String>((ref, restaurantId) async {
  final menuService = ref.watch(menuServiceProvider);
  return await menuService.getMenuItems(restaurantId);
});

final menuServiceProvider = Provider<MenuService>((ref) {
  return MenuService(Supabase.instance.client);
});

class RestaurantMenuScreen extends ConsumerWidget {
  final String restaurantId;

  const RestaurantMenuScreen({super.key, required this.restaurantId});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final menuItemsAsync = ref.watch(menuItemsProvider(restaurantId));

    return Scaffold(
      appBar: AppBar(
        title: const Text('Menu'),
      ),
      body: menuItemsAsync.when(
        data: (menuItems) {
          if (menuItems.isEmpty) {
            return const Center(child: Text('No menu items found.'));
          }
          return ListView.builder(
            itemCount: menuItems.length,
            itemBuilder: (context, index) {
              final item = menuItems[index];
              return ListTile(
                leading: Image.network(item.imageUrl!),
                title: Text(item.name),
                subtitle: Text('${item.description} - ${item.price.toStringAsFixed(2)}'),
                onTap: () {
                  ref.read(cartProvider.notifier).addItem(item);
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(content: Text('${item.name} added to cart!')),
                  );
                },
              );
            },
          );
        },
        loading: () => Shimmer.fromColors(
          baseColor: Colors.grey[300]!,
          highlightColor: Colors.grey[100]!,
          child: ListView.builder(
            itemCount: 5,
            itemBuilder: (context, index) => ListTile(
              leading: Container(
                width: 50,
                height: 50,
                color: Colors.white,
              ),
              title: Container(
                height: 20,
                color: Colors.white,
              ),
              subtitle: Container(
                height: 15,
                color: Colors.white,
              ),
            ),
          ),
        ),
        error: (error, stackTrace) => CustomErrorWidget(error.toString()),
      ),
    );
  }
}
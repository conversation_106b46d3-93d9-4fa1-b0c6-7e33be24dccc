import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class MenuItemDetailModalWidget extends StatefulWidget {
  final Map<String, dynamic> itemData;
  final Function(Map<String, dynamic>) onAddToCart;

  const MenuItemDetailModalWidget({
    super.key,
    required this.itemData,
    required this.onAddToCart,
  });

  @override
  State<MenuItemDetailModalWidget> createState() =>
      _MenuItemDetailModalWidgetState();
}

class _MenuItemDetailModalWidgetState extends State<MenuItemDetailModalWidget> {
  int _quantity = 1;
  final Map<String, dynamic> _customizations = {};
  double _totalPrice = 0.0;

  @override
  void initState() {
    super.initState();
    _calculateTotalPrice();
  }

  void _calculateTotalPrice() {
    final basePrice = widget.itemData['price']?.toDouble() ?? 0.0;
    _totalPrice = basePrice * _quantity;
  }

  void _updateQuantity(int quantity) {
    setState(() {
      _quantity = quantity > 0 ? quantity : 1;
      _calculateTotalPrice();
    });
  }

  void _addToCart() {
    final customizations = Map<String, dynamic>.from(_customizations);
    customizations['quantity'] = _quantity;
    customizations['total_price'] = _totalPrice;

    widget.onAddToCart(customizations);
    Navigator.pop(context);
  }

  @override
  Widget build(BuildContext context) {
    final name = widget.itemData['name'] ?? 'Menu Item';
    final description = widget.itemData['description'] ?? '';
    final basePrice = widget.itemData['price']?.toDouble() ?? 0.0;
    final imageUrl = widget.itemData['image_url'];
    final ingredients = widget.itemData['ingredients'] as List<dynamic>? ?? [];
    final allergens = widget.itemData['allergens'] as List<dynamic>? ?? [];
    final calories = widget.itemData['calories'];
    final isVegetarian = widget.itemData['is_vegetarian'] ?? false;
    final isVegan = widget.itemData['is_vegan'] ?? false;
    final isSpicy = widget.itemData['is_spicy'] ?? false;

    return DraggableScrollableSheet(
        initialChildSize: 0.9,
        minChildSize: 0.5,
        maxChildSize: 0.95,
        builder: (context, scrollController) {
          return Container(
              decoration: BoxDecoration(
                  color: Theme.of(context).scaffoldBackgroundColor,
                  borderRadius:
                      const BorderRadius.vertical(top: Radius.circular(20))),
              child: Column(children: [
                // Handle
                Container(
                    margin: const EdgeInsets.symmetric(vertical: 8),
                    width: 40,
                    height: 4,
                    decoration: BoxDecoration(
                        color: Colors.grey[300],
                        borderRadius: BorderRadius.circular(2))),

                // Content
                Expanded(
                    child: SingleChildScrollView(
                        controller: scrollController,
                        child: Column(
                            crossAxisAlignment: CrossAxisAlignment.start,
                            children: [
                              // Item image
                              if (imageUrl != null)
                                Container(
                                    height: 25.h,
                                    width: double.infinity,
                                    margin:
                                        EdgeInsets.symmetric(horizontal: 4.w),
                                    child: ClipRRect(
                                        borderRadius: BorderRadius.circular(16),
                                        child: CustomImageWidget(
                                            imageUrl: imageUrl,
                                            fit: BoxFit.cover))),

                              SizedBox(height: 3.h),

                              // Item details
                              Padding(
                                  padding:
                                      EdgeInsets.symmetric(horizontal: 4.w),
                                  child: Column(
                                      crossAxisAlignment:
                                          CrossAxisAlignment.start,
                                      children: [
                                        // Name and indicators
                                        Row(
                                            crossAxisAlignment:
                                                CrossAxisAlignment.start,
                                            children: [
                                              Expanded(
                                                  child: Text(name,
                                                      style: Theme.of(context)
                                                          .textTheme
                                                          .headlineSmall
                                                          ?.copyWith(
                                                              fontWeight:
                                                                  FontWeight
                                                                      .bold))),
                                              SizedBox(width: 2.w),
                                              Column(children: [
                                                if (isVegan)
                                                  _buildDietBadge(
                                                      context,
                                                      'Vegan',
                                                      AppTheme.successLight),
                                                if (isVegetarian && !isVegan)
                                                  _buildDietBadge(
                                                      context,
                                                      'Vegetarian',
                                                      AppTheme.successLight),
                                                if (isSpicy)
                                                  _buildDietBadge(
                                                      context,
                                                      'Spicy',
                                                      AppTheme.errorLight),
                                              ]),
                                            ]),

                                        SizedBox(height: 1.h),

                                        // Price and calories
                                        Row(children: [
                                          Text(
                                              '\${basePrice.toStringAsFixed(2)}',
                                              style: Theme.of(context)
                                                  .textTheme
                                                  .titleLarge
                                                  ?.copyWith(
                                                      color: Theme.of(context)
                                                          .colorScheme
                                                          .primary,
                                                      fontWeight:
                                                          FontWeight.bold)),
                                          if (calories != null) ...[
                                            SizedBox(width: 4.w),
                                            Container(
                                                padding: EdgeInsets.symmetric(
                                                    horizontal: 2.w,
                                                    vertical: 0.5.h),
                                                decoration: BoxDecoration(
                                                    color: Theme.of(context)
                                                        .colorScheme
                                                        .surfaceContainerHighest,
                                                    borderRadius:
                                                        BorderRadius.circular(
                                                            12)),
                                                child: Text('$calories cal',
                                                    style: Theme.of(context)
                                                        .textTheme
                                                        .bodySmall
                                                        ?.copyWith(
                                                            fontWeight:
                                                                FontWeight
                                                                    .w500))),
                                          ],
                                        ]),

                                        SizedBox(height: 2.h),

                                        // Description
                                        if (description.isNotEmpty) ...[
                                          Text('Description',
                                              style: Theme.of(context)
                                                  .textTheme
                                                  .titleMedium
                                                  ?.copyWith(
                                                      fontWeight:
                                                          FontWeight.bold)),
                                          SizedBox(height: 1.h),
                                          Text(description,
                                              style: Theme.of(context)
                                                  .textTheme
                                                  .bodyLarge
                                                  ?.copyWith(
                                                      color: Theme.of(context)
                                                          .colorScheme
                                                          .onSurfaceVariant,
                                                      height: 1.5)),
                                          SizedBox(height: 2.h),
                                        ],

                                        // Ingredients
                                        if (ingredients.isNotEmpty) ...[
                                          Text('Ingredients',
                                              style: Theme.of(context)
                                                  .textTheme
                                                  .titleMedium
                                                  ?.copyWith(
                                                      fontWeight:
                                                          FontWeight.bold)),
                                          SizedBox(height: 1.h),
                                          Wrap(
                                              spacing: 2.w,
                                              runSpacing: 1.h,
                                              children:
                                                  ingredients.map((ingredient) {
                                                return Container(
                                                    padding:
                                                        EdgeInsets.symmetric(
                                                            horizontal: 3.w,
                                                            vertical: 1.h),
                                                    decoration: BoxDecoration(
                                                        color: Theme.of(context)
                                                            .colorScheme
                                                            .surfaceContainerHighest,
                                                        borderRadius:
                                                            BorderRadius
                                                                .circular(20)),
                                                    child: Text(
                                                        ingredient.toString(),
                                                        style: Theme.of(context)
                                                            .textTheme
                                                            .bodySmall
                                                            ?.copyWith(
                                                                fontWeight:
                                                                    FontWeight
                                                                        .w500)));
                                              }).toList()),
                                          SizedBox(height: 2.h),
                                        ],

                                        // Allergens
                                        if (allergens.isNotEmpty) ...[
                                          Text('Allergens',
                                              style: Theme.of(context)
                                                  .textTheme
                                                  .titleMedium
                                                  ?.copyWith(
                                                      fontWeight:
                                                          FontWeight.bold,
                                                      color: AppTheme
                                                          .warningLight)),
                                          SizedBox(height: 1.h),
                                          Wrap(
                                              spacing: 2.w,
                                              runSpacing: 1.h,
                                              children:
                                                  allergens.map((allergen) {
                                                return Container(
                                                    padding: EdgeInsets.symmetric(
                                                        horizontal: 3.w,
                                                        vertical: 1.h),
                                                    decoration: BoxDecoration(
                                                        color: AppTheme.warningLight
                                                            .withAlpha(26),
                                                        border: Border.all(
                                                            color: AppTheme
                                                                .warningLight,
                                                            width: 1),
                                                        borderRadius:
                                                            BorderRadius.circular(
                                                                20)),
                                                    child: Text(
                                                        allergen.toString(),
                                                        style: Theme.of(context)
                                                            .textTheme
                                                            .bodySmall
                                                            ?.copyWith(
                                                                color: AppTheme
                                                                    .warningLight,
                                                                fontWeight:
                                                                    FontWeight.w500)));
                                              }).toList()),
                                          SizedBox(height: 2.h),
                                        ],

                                        // Special instructions
                                        Text('Special Instructions',
                                            style: Theme.of(context)
                                                .textTheme
                                                .titleMedium
                                                ?.copyWith(
                                                    fontWeight:
                                                        FontWeight.bold)),
                                        SizedBox(height: 1.h),
                                        TextField(
                                            decoration: const InputDecoration(
                                                hintText:
                                                    'Any special requests? (optional)',
                                                border: OutlineInputBorder()),
                                            maxLines: 3,
                                            onChanged: (value) {
                                              _customizations[
                                                      'special_instructions'] =
                                                  value;
                                            }),

                                        SizedBox(height: 4.h),
                                      ])),
                            ]))),

                // Bottom controls
                Container(
                    padding: EdgeInsets.all(4.w),
                    decoration: BoxDecoration(
                        color: Theme.of(context).scaffoldBackgroundColor,
                        border: Border(
                            top: BorderSide(
                                color: Theme.of(context)
                                    .colorScheme
                                    .outline
                                    .withAlpha(51)))),
                    child: SafeArea(
                        child: Row(children: [
                      // Quantity controls
                      Container(
                          decoration: BoxDecoration(
                              border: Border.all(
                                  color: Theme.of(context).colorScheme.outline),
                              borderRadius: BorderRadius.circular(12)),
                          child: Row(mainAxisSize: MainAxisSize.min, children: [
                            IconButton(
                                onPressed: () => _updateQuantity(_quantity - 1),
                                icon: const Icon(Icons.remove)),
                            Padding(
                                padding: EdgeInsets.symmetric(horizontal: 2.w),
                                child: Text(_quantity.toString(),
                                    style: Theme.of(context)
                                        .textTheme
                                        .titleMedium
                                        ?.copyWith(
                                            fontWeight: FontWeight.bold))),
                            IconButton(
                                onPressed: () => _updateQuantity(_quantity + 1),
                                icon: const Icon(Icons.add)),
                          ])),

                      SizedBox(width: 4.w),

                      // Add to cart button
                      Expanded(
                          child: ElevatedButton(
                              onPressed: _addToCart,
                              style: ElevatedButton.styleFrom(
                                  padding: EdgeInsets.symmetric(vertical: 2.h)),
                              child: Text(
                                  'Add $_quantity to cart • \${_totalPrice.toStringAsFixed(2)}',
                                  style: const TextStyle(
                                      fontWeight: FontWeight.bold)))),
                    ]))),
              ]));
        });
  }

  Widget _buildDietBadge(BuildContext context, String label, Color color) {
    return Container(
        margin: EdgeInsets.only(bottom: 1.h),
        padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 0.5.h),
        decoration: BoxDecoration(
            color: color.withAlpha(26),
            border: Border.all(color: color, width: 1),
            borderRadius: BorderRadius.circular(16)),
        child: Text(label,
            style: Theme.of(context)
                .textTheme
                .bodySmall
                ?.copyWith(color: color, fontWeight: FontWeight.bold)));
  }
}
import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';

class MenuItemCardWidget extends StatelessWidget {
  final Map<String, dynamic> itemData;
  final int quantity;
  final VoidCallback onTap;
  final VoidCallback onAddToCart;
  final Function(int) onUpdateQuantity;

  const MenuItemCardWidget({
    super.key,
    required this.itemData,
    required this.quantity,
    required this.onTap,
    required this.onAddToCart,
    required this.onUpdateQuantity,
  });

  @override
  Widget build(BuildContext context) {
    final name = itemData['name'] ?? 'Menu Item';
    final description = itemData['description'] ?? '';
    final price = itemData['price']?.toDouble() ?? 0.0;
    final imageUrl = itemData['image_url'];
    final isVegetarian = itemData['is_vegetarian'] ?? false;
    final isVegan = itemData['is_vegan'] ?? false;
    final isSpicy = itemData['is_spicy'] ?? false;
    final preparationTime = itemData['preparation_time_minutes'] ?? 15;

    return Container(
        margin: EdgeInsets.only(bottom: 2.h),
        child: Card(
            elevation: 2,
            shape:
                RoundedRectangleBorder(borderRadius: BorderRadius.circular(16)),
            child: InkWell(
                onTap: onTap,
                borderRadius: BorderRadius.circular(16),
                child: Padding(
                    padding: EdgeInsets.all(4.w),
                    child: Row(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          // Item content
                          Expanded(
                              flex: 3,
                              child: Column(
                                  crossAxisAlignment: CrossAxisAlignment.start,
                                  children: [
                                    // Item name and indicators
                                    Row(children: [
                                      Expanded(
                                          child: Text(name,
                                              style: Theme.of(context)
                                                  .textTheme
                                                  .titleMedium
                                                  ?.copyWith(
                                                      fontWeight:
                                                          FontWeight.bold),
                                              maxLines: 2,
                                              overflow: TextOverflow.ellipsis)),
                                      SizedBox(width: 2.w),
                                      // Diet indicators
                                      Row(
                                          mainAxisSize: MainAxisSize.min,
                                          children: [
                                            if (isVegan)
                                              _buildDietIndicator(
                                                  context,
                                                  'V+',
                                                  AppTheme.successLight,
                                                  'Vegan'),
                                            if (isVegetarian && !isVegan)
                                              _buildDietIndicator(
                                                  context,
                                                  'V',
                                                  AppTheme.successLight,
                                                  'Vegetarian'),
                                            if (isSpicy)
                                              _buildDietIndicator(
                                                  context,
                                                  '🌶️',
                                                  AppTheme.errorLight,
                                                  'Spicy'),
                                          ]),
                                    ]),

                                    SizedBox(height: 1.h),

                                    // Description
                                    if (description.isNotEmpty) ...[
                                      Text(description,
                                          style: Theme.of(context)
                                              .textTheme
                                              .bodyMedium
                                              ?.copyWith(
                                                  color: Theme.of(context)
                                                      .colorScheme
                                                      .onSurfaceVariant),
                                          maxLines: 3,
                                          overflow: TextOverflow.ellipsis),
                                      SizedBox(height: 1.h),
                                    ],

                                    // Price and preparation time
                                    Row(children: [
                                      Text('\$${price.toStringAsFixed(2)}',
                                          style: Theme.of(context)
                                              .textTheme
                                              .titleLarge
                                              ?.copyWith(
                                                  color: Theme.of(context)
                                                      .colorScheme
                                                      .primary,
                                                  fontWeight: FontWeight.bold)),
                                      SizedBox(width: 2.w),
                                      Container(
                                          padding: EdgeInsets.symmetric(
                                              horizontal: 2.w, vertical: 0.5.h),
                                          decoration: BoxDecoration(
                                              color: Theme.of(context)
                                                  .colorScheme
                                                  .surfaceContainerHighest,
                                              borderRadius:
                                                  BorderRadius.circular(12)),
                                          child: Row(
                                              mainAxisSize: MainAxisSize.min,
                                              children: [
                                                Icon(Icons.access_time,
                                                    size: 14,
                                                    color: Theme.of(context)
                                                        .colorScheme
                                                        .onSurfaceVariant),
                                                SizedBox(width: 1.w),
                                                Text('${preparationTime}m',
                                                    style: Theme.of(context)
                                                        .textTheme
                                                        .bodySmall
                                                        ?.copyWith(
                                                            color: Theme.of(
                                                                    context)
                                                                .colorScheme
                                                                .onSurfaceVariant,
                                                            fontWeight:
                                                                FontWeight
                                                                    .w500)),
                                              ])),
                                    ]),
                                  ])),

                          SizedBox(width: 3.w),

                          // Image and add to cart
                          Expanded(
                              flex: 2,
                              child: Column(children: [
                                // Item image
                                ClipRRect(
                                    borderRadius: BorderRadius.circular(12),
                                    child: AspectRatio(
                                        aspectRatio: 1,
                                        child: CustomImageWidget(
                                            imageUrl: imageUrl,
                                            fit: BoxFit.cover))),

                                SizedBox(height: 2.h),

                                // Add to cart / quantity controls
                                _buildCartControls(context),
                              ])),
                        ])))));
  }

  Widget _buildDietIndicator(
      BuildContext context, String label, Color color, String tooltip) {
    return Tooltip(
        message: tooltip,
        child: Container(
            margin: EdgeInsets.only(left: 1.w),
            padding: EdgeInsets.symmetric(horizontal: 1.5.w, vertical: 0.5.h),
            decoration: BoxDecoration(
                color: color.withAlpha(26),
                border: Border.all(color: color, width: 1),
                borderRadius: BorderRadius.circular(4)),
            child: Text(label,
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: color, fontWeight: FontWeight.bold, fontSize: 10))));
  }

  Widget _buildCartControls(BuildContext context) {
    if (quantity == 0) {
      return SizedBox(
          width: double.infinity,
          child: ElevatedButton(
              onPressed: onAddToCart,
              style: ElevatedButton.styleFrom(
                  padding: EdgeInsets.symmetric(vertical: 1.h),
                  shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(12))),
              child: const Text('Add')));
    }

    return Container(
        decoration: BoxDecoration(
            color: Theme.of(context).colorScheme.primaryContainer,
            borderRadius: BorderRadius.circular(12)),
        child:
            Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
          IconButton(
              onPressed: () => onUpdateQuantity(quantity - 1),
              icon: Icon(quantity > 1 ? Icons.remove : Icons.delete_outline,
                  color: Theme.of(context).colorScheme.onPrimaryContainer),
              constraints: const BoxConstraints(minWidth: 40, minHeight: 40)),
          Text(quantity.toString(),
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  color: Theme.of(context).colorScheme.onPrimaryContainer,
                  fontWeight: FontWeight.bold)),
          IconButton(
              onPressed: () => onUpdateQuantity(quantity + 1),
              icon: Icon(Icons.add,
                  color: Theme.of(context).colorScheme.onPrimaryContainer),
              constraints: const BoxConstraints(minWidth: 40, minHeight: 40)),
        ]));
  }
}
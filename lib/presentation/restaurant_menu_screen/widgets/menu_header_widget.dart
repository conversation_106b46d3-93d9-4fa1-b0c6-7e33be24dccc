import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';
import 'package:google_fonts/google_fonts.dart';

import '../../../core/app_export.dart';
import '../../../theme/app_theme.dart';
import '../../../widgets/custom_image_widget.dart';

class MenuHeaderWidget extends StatelessWidget {
  final Map<String, dynamic> restaurantData;
  final Function(String) onSearchChanged;
  final TextEditingController searchController;

  const MenuHeaderWidget({
    super.key,
    required this.restaurantData,
    required this.onSearchChanged,
    required this.searchController,
  });

  @override
  Widget build(BuildContext context) {
    final restaurant = restaurantData;
    final rating = restaurant['rating']?.toDouble() ?? 0.0;
    final deliveryTime = restaurant['delivery_time_minutes'] ?? 30;
    final minimumOrder = restaurant['minimum_order']?.toDouble() ?? 0.0;

    return SliverAppBar(
        expandedHeight: 35.h,
        floating: false,
        pinned: true,
        elevation: 0,
        flexibleSpace: FlexibleSpaceBar(
            background: Column(children: [
          // Restaurant image with overlay
          Expanded(
              flex: 3,
              child: Stack(children: [
                CustomImageWidget(
                    imageUrl: restaurant['image_url'] ?? '',
                    width: double.infinity,
                    height: double.infinity,
                    fit: BoxFit.cover),
                // Gradient overlay
                Container(
                    decoration: BoxDecoration(
                        gradient: LinearGradient(
                            begin: Alignment.topCenter,
                            end: Alignment.bottomCenter,
                            colors: [
                      Colors.transparent,
                      Colors.black.withAlpha(179),
                    ]))),
                // Restaurant info overlay
                Positioned(
                    bottom: 2.h,
                    left: 4.w,
                    right: 4.w,
                    child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(restaurant['name'] ?? 'Restaurant',
                              style: GoogleFonts.inter(
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white)),
                          SizedBox(height: 1.h),
                          Row(children: [
                            _buildInfoChip(
                                context,
                                Icons.star,
                                rating.toStringAsFixed(1),
                                AppTheme.warningLight),
                            SizedBox(width: 2.w),
                            _buildInfoChip(context, Icons.access_time,
                                '$deliveryTime min', Colors.white70),
                            SizedBox(width: 2.w),
                            _buildInfoChip(
                                context,
                                Icons.attach_money,
                                'Min \$${minimumOrder.toStringAsFixed(0)}',
                                Colors.white70),
                          ]),
                          if (restaurant['description'] != null) ...[
                            SizedBox(height: 1.h),
                            Text(restaurant['description'],
                                style: GoogleFonts.inter(
                                    fontSize: 14, color: Colors.white70),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis),
                          ],
                        ])),
              ])),

          // Search bar
          Expanded(
              flex: 1,
              child: Container(
                  color: Theme.of(context).scaffoldBackgroundColor,
                  padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
                  child: TextField(
                      controller: searchController,
                      onChanged: onSearchChanged,
                      decoration: InputDecoration(
                          hintText: 'Search menu items...',
                          prefixIcon: const Icon(Icons.search),
                          suffixIcon: searchController.text.isNotEmpty
                              ? IconButton(
                                  icon: const Icon(Icons.clear),
                                  onPressed: () {
                                    searchController.clear();
                                    onSearchChanged('');
                                  })
                              : null,
                          border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                              borderSide: BorderSide.none),
                          filled: true,
                          fillColor: Theme.of(context).colorScheme.surface,
                          contentPadding: EdgeInsets.symmetric(
                              horizontal: 4.w, vertical: 1.h))))),
        ])));
  }

  Widget _buildInfoChip(
      BuildContext context, IconData icon, String text, Color color) {
    return Container(
        padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 0.5.h),
        decoration: BoxDecoration(
            color: Colors.black.withAlpha(77),
            borderRadius: BorderRadius.circular(16)),
        child: Row(mainAxisSize: MainAxisSize.min, children: [
          Icon(icon, size: 16, color: color),
          SizedBox(width: 1.w),
          Text(text,
              style: GoogleFonts.inter(
                  fontSize: 12, fontWeight: FontWeight.w500, color: color)),
        ]));
  }
}
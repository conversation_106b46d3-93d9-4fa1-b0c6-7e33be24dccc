
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:foodflow/features/rider/providers/rider_order_provider.dart';
import 'package:foodflow/widgets/custom_error_widget.dart';
import 'package:shimmer/shimmer.dart';
import 'package:google_maps_flutter/google_maps_flutter.dart';

class AssignedDeliveriesScreen extends ConsumerWidget {
  const AssignedDeliveriesScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final assignedOrdersAsync = ref.watch(assignedOrdersProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('My Assignments'),
      ),
      body: assignedOrdersAsync.when(
        data: (orders) {
          if (orders.isEmpty) {
            return const Center(child: Text('No assigned deliveries.'));
          }
          return ListView.builder(
            itemCount: orders.length,
            itemBuilder: (context, index) {
              final order = orders[index];
              return Card(
                margin: const EdgeInsets.all(8.0),
                child: Padding(
                  padding: const EdgeInsets.all(16.0),
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text('Order #${order.orderNumber}', style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                      const SizedBox(height: 8),
                      Text('Status: ${order.status}'),
                      const SizedBox(height: 8),
                      Text('Delivery Address: ${order.deliveryAddress}'),
                      const SizedBox(height: 16),
                      SizedBox(
                        height: 200,
                        child: GoogleMap(
                          initialCameraPosition: CameraPosition(
                            target: LatLng(0, 0), // TODO: Set actual coordinates
                            zoom: 14,
                          ),
                          markers: {
                            // TODO: Add markers for pickup and drop-off
                          },
                        ),
                      ),
                      const SizedBox(height: 16),
                      Row(
                        mainAxisAlignment: MainAxisAlignment.spaceAround,
                        children: [
                          ElevatedButton(
                            onPressed: () async {
                              await ref.read(orderServiceProvider).updateOrderStatus(order.id, 'picked_up');
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(content: Text('Order status updated to Picked Up!')),
                              );
                            },
                            child: const Text('Picked Up'),
                          ),
                          ElevatedButton(
                            onPressed: () async {
                              await ref.read(orderServiceProvider).updateOrderStatus(order.id, 'delivered');
                              ScaffoldMessenger.of(context).showSnackBar(
                                const SnackBar(content: Text('Order status updated to Delivered!')),
                              );
                            },
                            child: const Text('Delivered'),
                          ),
                        ],
                      ),
                    ],
                  ),
                ),
              );
            },
          );
        },
        loading: () => Shimmer.fromColors(
          baseColor: Colors.grey[300]!,
          highlightColor: Colors.grey[100]!,
          child: ListView.builder(
            itemCount: 3,
            itemBuilder: (context, index) => Card(
              margin: const EdgeInsets.all(8.0),
              child: Padding(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Container(height: 20, width: 150, color: Colors.white),
                    const SizedBox(height: 8),
                    Container(height: 15, width: 100, color: Colors.white),
                    const SizedBox(height: 8),
                    Container(height: 15, width: 200, color: Colors.white),
                    const SizedBox(height: 16),
                    Container(height: 200, color: Colors.white),
                    const SizedBox(height: 16),
                    Row(
                      mainAxisAlignment: MainAxisAlignment.spaceAround,
                      children: [
                        Container(height: 40, width: 80, color: Colors.white),
                        Container(height: 40, width: 80, color: Colors.white),
                      ],
                    ),
                  ],
                ),
              ),
            ),
          ),
        ),
        error: (error, stackTrace) => CustomErrorWidget(error.toString()),
      ),
    );
  }
}

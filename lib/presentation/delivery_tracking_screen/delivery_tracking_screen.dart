import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:sizer/sizer.dart';

import '../../core/app_export.dart';
import '../../services/order_service.dart';
import '../../services/supabase_service.dart';
import '../../theme/app_theme.dart';
import './widgets/delivery_map_widget.dart';
import './widgets/delivery_status_timeline_widget.dart';
import './widgets/order_summary_widget.dart';
import './widgets/rider_info_widget.dart';

class DeliveryTrackingScreen extends StatefulWidget {
  const DeliveryTrackingScreen({super.key});

  @override
  State<DeliveryTrackingScreen> createState() => _DeliveryTrackingScreenState();
}

class _DeliveryTrackingScreenState extends State<DeliveryTrackingScreen>
    with TickerProviderStateMixin {
  final OrderService _orderService = OrderService();
  final SupabaseService _supabaseService = SupabaseService();

  Map<String, dynamic>? _orderData;
  String? _orderId;
  bool _isLoading = true;
  String _error = '';

  late AnimationController _pulseController;
  late Animation<double> _pulseAnimation;

  @override
  void initState() {
    super.initState();
    _initializeAnimations();
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _extractOrderId();
    });
  }

  void _initializeAnimations() {
    _pulseController = AnimationController(
      duration: const Duration(seconds: 2),
      vsync: this,
    );
    _pulseAnimation = Tween<double>(
      begin: 0.8,
      end: 1.2,
    ).animate(CurvedAnimation(
      parent: _pulseController,
      curve: Curves.easeInOut,
    ));
    _pulseController.repeat(reverse: true);
  }

  void _extractOrderId() {
    final args = ModalRoute.of(context)?.settings.arguments as String?;
    if (args != null && args.isNotEmpty) {
      _orderId = args;
      _loadOrderData();
    } else {
      setState(() {
        _error = 'No order ID provided';
        _isLoading = false;
      });
    }
  }

  Future<void> _loadOrderData() async {
    if (_orderId == null) return;

    try {
      setState(() {
        _isLoading = true;
        _error = '';
      });

      final orderData = await _orderService.getOrderById(_orderId!);

      if (orderData != null) {
        setState(() {
          _orderData = orderData;
          _isLoading = false;
        });
        _subscribeToOrderUpdates();
      } else {
        setState(() {
          _error = 'Order not found';
          _isLoading = false;
        });
      }
    } catch (e) {
      setState(() {
        _error = 'Failed to load order: ${e.toString()}';
        _isLoading = false;
      });
    }
  }

  void _subscribeToOrderUpdates() {
    if (_orderId == null) return;

    _orderService.subscribeToOrderUpdates(
      orderId: _orderId!,
      onUpdate: (updatedOrder) {
        if (mounted) {
          setState(() {
            _orderData = updatedOrder;
          });
          _showStatusUpdateNotification();
          HapticFeedback.lightImpact();
        }
      },
    );
  }

  void _showStatusUpdateNotification() {
    if (_orderData?['status'] != null) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Order status updated: ${_orderData!['status']}'),
          duration: const Duration(seconds: 3),
        ),
      );
    }
  }

  Future<void> _callRider() async {
    final riderPhone = _orderData?['user_profiles']?['phone'];
    if (riderPhone != null) {
      // TODO: Implement phone call functionality
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Calling rider: $riderPhone')),
      );
    }
  }

  Future<void> _messageRider() async {
    final riderPhone = _orderData?['user_profiles']?['phone'];
    if (riderPhone != null) {
      // TODO: Implement messaging functionality
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Messaging rider: $riderPhone')),
      );
    }
  }

  Future<void> _cancelOrder() async {
    if (_orderId == null || _orderData?['status'] == 'delivered') return;

    final shouldCancel = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Cancel Order'),
        content: const Text('Are you sure you want to cancel this order?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('No'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('Yes, Cancel'),
          ),
        ],
      ),
    );

    if (shouldCancel == true) {
      try {
        await _orderService.cancelOrder(orderId: _orderId!);
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Order cancelled successfully')),
        );
      } catch (e) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Failed to cancel order: $e')),
        );
      }
    }
  }

  @override
  void dispose() {
    _pulseController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: Text(
          _orderData?['order_number'] ?? 'Order Tracking',
          style: Theme.of(context).textTheme.titleLarge,
        ),
        centerTitle: true,
        actions: [
          if (_orderData != null && _orderData!['status'] != 'delivered')
            IconButton(
              icon: const Icon(Icons.cancel_outlined),
              onPressed: _cancelOrder,
              tooltip: 'Cancel Order',
            ),
        ],
      ),
      body: _buildBody(),
    );
  }

  Widget _buildBody() {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_error.isNotEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.error_outline,
              size: 64.w,
              color: Theme.of(context).colorScheme.error,
            ),
            SizedBox(height: 2.h),
            Text(
              _error,
              style: Theme.of(context).textTheme.titleMedium,
              textAlign: TextAlign.center,
            ),
            SizedBox(height: 2.h),
            ElevatedButton(
              onPressed: _loadOrderData,
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (_orderData == null) {
      return const Center(
        child: Text('No order data available'),
      );
    }

    return Stack(
      children: [
        // Full-screen map
        DeliveryMapWidget(
          orderData: _orderData!,
          pulseAnimation: _pulseAnimation,
        ),

        // Bottom sheet overlay
        DraggableScrollableSheet(
          initialChildSize: 0.35,
          minChildSize: 0.25,
          maxChildSize: 0.8,
          builder: (context, scrollController) {
            return Container(
              decoration: BoxDecoration(
                color: Theme.of(context).scaffoldBackgroundColor,
                borderRadius: const BorderRadius.vertical(
                  top: Radius.circular(20),
                ),
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withAlpha(26),
                    blurRadius: 10,
                    offset: const Offset(0, -5),
                  ),
                ],
              ),
              child: SingleChildScrollView(
                controller: scrollController,
                child: Column(
                  children: [
                    // Drag handle
                    Container(
                      margin: const EdgeInsets.symmetric(vertical: 8),
                      width: 40,
                      height: 4,
                      decoration: BoxDecoration(
                        color: Colors.grey[300],
                        borderRadius: BorderRadius.circular(2),
                      ),
                    ),

                    // Estimated delivery time
                    _buildDeliveryTimeHeader(),

                    // Order summary
                    OrderSummaryWidget(orderData: _orderData!),

                    // Rider information
                    if (_orderData!['user_profiles'] != null)
                      RiderInfoWidget(
                        riderData: _orderData!['user_profiles'],
                        onCall: _callRider,
                        onMessage: _messageRider,
                      ),

                    // Status timeline
                    DeliveryStatusTimelineWidget(
                      orderData: _orderData!,
                    ),

                    SizedBox(height: 2.h),
                  ],
                ),
              ),
            );
          },
        ),
      ],
    );
  }

  Widget _buildDeliveryTimeHeader() {
    final estimatedTime = _orderData?['estimated_delivery_time'];
    final status = _orderData?['status'] ?? 'pending';

    return Container(
      padding: EdgeInsets.symmetric(horizontal: 4.w, vertical: 2.h),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Estimated Delivery',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.onSurfaceVariant,
                    ),
              ),
              if (estimatedTime != null)
                Text(
                  _formatEstimatedTime(estimatedTime),
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                )
              else
                Text(
                  'Calculating...',
                  style: Theme.of(context).textTheme.titleMedium,
                ),
            ],
          ),
          Container(
            padding: EdgeInsets.symmetric(horizontal: 3.w, vertical: 1.h),
            decoration: BoxDecoration(
              color: AppTheme.getStatusColor(status).withAlpha(26),
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: AppTheme.getStatusColor(status),
                width: 1,
              ),
            ),
            child: Text(
              status.toUpperCase(),
              style: Theme.of(context).textTheme.labelMedium?.copyWith(
                    color: AppTheme.getStatusColor(status),
                    fontWeight: FontWeight.bold,
                  ),
            ),
          ),
        ],
      ),
    );
  }

  String _formatEstimatedTime(String? estimatedTime) {
    if (estimatedTime == null) return 'Calculating...';

    try {
      final deliveryTime = DateTime.parse(estimatedTime);
      final now = DateTime.now();
      final difference = deliveryTime.difference(now);

      if (difference.isNegative) {
        return 'Delivered';
      }

      final minutes = difference.inMinutes;
      if (minutes < 60) {
        return '$minutes min';
      } else {
        final hours = minutes ~/ 60;
        final remainingMinutes = minutes % 60;
        return '${hours}h ${remainingMinutes}m';
      }
    } catch (e) {
      return 'Calculating...';
    }
  }
}

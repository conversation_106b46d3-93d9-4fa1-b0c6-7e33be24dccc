import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../theme/app_theme.dart';

class DeliveryMapWidget extends StatefulWidget {
  final Map<String, dynamic> orderData;
  final Animation<double> pulseAnimation;

  const DeliveryMapWidget({
    super.key,
    required this.orderData,
    required this.pulseAnimation,
  });

  @override
  State<DeliveryMapWidget> createState() => _DeliveryMapWidgetState();
}

class _DeliveryMapWidgetState extends State<DeliveryMapWidget> {
  @override
  Widget build(BuildContext context) {
    return Container(
      width: double.infinity,
      height: double.infinity,
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
      ),
      child: Stack(
        children: [
          // Map placeholder with location indicators
          _buildMapPlaceholder(),

          // Customer location marker
          Positioned(
            top: 20.h,
            left: 10.w,
            child: _buildLocationMarker(
              'Customer',
              Icons.home,
              Theme.of(context).colorScheme.primary,
            ),
          ),

          // Restaurant location marker
          Positioned(
            top: 35.h,
            right: 15.w,
            child: _buildLocationMarker(
              'Restaurant',
              Icons.restaurant,
              Theme.of(context).colorScheme.secondary,
            ),
          ),

          // Rider location marker (animated)
          if (widget.orderData['user_profiles'] != null)
            Positioned(
              top: 28.h,
              left: 35.w,
              child: AnimatedBuilder(
                animation: widget.pulseAnimation,
                builder: (context, child) {
                  return Transform.scale(
                    scale: widget.pulseAnimation.value,
                    child: _buildRiderMarker(),
                  );
                },
              ),
            ),

          // Route line
          CustomPaint(
            size: Size(double.infinity, double.infinity),
            painter: RouteLinePainter(
              context: context,
              orderStatus: widget.orderData['status'] ?? 'pending',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMapPlaceholder() {
    return Container(
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
          colors: [
            Theme.of(context).colorScheme.surfaceContainerHighest,
            Theme.of(context).colorScheme.surface,
          ],
        ),
      ),
      child: CustomPaint(
        size: Size(double.infinity, double.infinity),
        painter: MapGridPainter(context: context),
      ),
    );
  }

  Widget _buildLocationMarker(String label, IconData icon, Color color) {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 1.h),
          decoration: BoxDecoration(
            color: color,
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(51),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Text(
            label,
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
          ),
        ),
        SizedBox(height: 0.5.h),
        Container(
          padding: const EdgeInsets.all(8),
          decoration: BoxDecoration(
            color: color,
            shape: BoxShape.circle,
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(77),
                blurRadius: 6,
                offset: const Offset(0, 3),
              ),
            ],
          ),
          child: Icon(
            icon,
            color: Colors.white,
            size: 24,
          ),
        ),
      ],
    );
  }

  Widget _buildRiderMarker() {
    return Column(
      mainAxisSize: MainAxisSize.min,
      children: [
        Container(
          padding: EdgeInsets.symmetric(horizontal: 2.w, vertical: 1.h),
          decoration: BoxDecoration(
            color: AppTheme.successLight,
            borderRadius: BorderRadius.circular(8),
            boxShadow: [
              BoxShadow(
                color: Colors.black.withAlpha(51),
                blurRadius: 4,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: Text(
            'Rider',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
          ),
        ),
        SizedBox(height: 0.5.h),
        Container(
          padding: const EdgeInsets.all(12),
          decoration: BoxDecoration(
            color: AppTheme.successLight,
            shape: BoxShape.circle,
            border: Border.all(color: Colors.white, width: 3),
            boxShadow: [
              BoxShadow(
                color: AppTheme.successLight.withAlpha(102),
                blurRadius: 12,
                offset: const Offset(0, 0),
              ),
            ],
          ),
          child: const Icon(
            Icons.motorcycle,
            color: Colors.white,
            size: 28,
          ),
        ),
      ],
    );
  }
}

class RouteLinePainter extends CustomPainter {
  final BuildContext context;
  final String orderStatus;

  RouteLinePainter({
    required this.context,
    required this.orderStatus,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = _getRouteColor()
      ..strokeWidth = 3
      ..style = PaintingStyle.stroke
      ..strokeCap = StrokeCap.round;

    final path = Path();

    // Restaurant to customer path
    final startX = size.width * 0.85;
    final startY = size.height * 0.35;
    final endX = size.width * 0.1;
    final endY = size.height * 0.2;

    path.moveTo(startX, startY);
    path.quadraticBezierTo(
      size.width * 0.5,
      size.height * 0.15,
      endX,
      endY,
    );

    canvas.drawPath(path, paint);

    // Progress indicator
    if (orderStatus != 'pending') {
      final progressPaint = Paint()
        ..color = AppTheme.successLight
        ..strokeWidth = 5
        ..style = PaintingStyle.stroke
        ..strokeCap = StrokeCap.round;

      final progressPath = _getProgressPath(path, _getProgress());
      canvas.drawPath(progressPath, progressPaint);
    }
  }

  Color _getRouteColor() {
    switch (orderStatus) {
      case 'delivered':
        return AppTheme.successLight;
      case 'picked_up':
      case 'on_the_way':
        return AppTheme.warningLight;
      case 'cancelled':
        return AppTheme.errorLight;
      default:
        return Theme.of(context).colorScheme.outline;
    }
  }

  double _getProgress() {
    switch (orderStatus) {
      case 'confirmed':
        return 0.1;
      case 'preparing':
        return 0.3;
      case 'ready':
        return 0.5;
      case 'picked_up':
        return 0.7;
      case 'delivered':
        return 1.0;
      default:
        return 0.0;
    }
  }

  Path _getProgressPath(Path fullPath, double progress) {
    final pathMetrics = fullPath.computeMetrics();
    final pathMetric = pathMetrics.first;
    final progressPath =
        pathMetric.extractPath(0, pathMetric.length * progress);
    return progressPath;
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => true;
}

class MapGridPainter extends CustomPainter {
  final BuildContext context;

  MapGridPainter({required this.context});

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = Theme.of(context).colorScheme.outline.withAlpha(26)
      ..strokeWidth = 1;

    // Draw grid lines
    const gridSpacing = 50.0;

    for (double x = 0; x < size.width; x += gridSpacing) {
      canvas.drawLine(
        Offset(x, 0),
        Offset(x, size.height),
        paint,
      );
    }

    for (double y = 0; y < size.height; y += gridSpacing) {
      canvas.drawLine(
        Offset(0, y),
        Offset(size.width, y),
        paint,
      );
    }
  }

  @override
  bool shouldRepaint(covariant CustomPainter oldDelegate) => false;
}

import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../theme/app_theme.dart';

class DeliveryStatusTimelineWidget extends StatelessWidget {
  final Map<String, dynamic> orderData;

  const DeliveryStatusTimelineWidget({
    super.key,
    required this.orderData,
  });

  @override
  Widget build(BuildContext context) {
    final currentStatus = orderData['status'] ?? 'pending';
    final createdAt = orderData['created_at'];

    final statuses = [
      {
        'key': 'confirmed',
        'title': 'Order Confirmed',
        'subtitle': 'Restaurant received your order',
        'icon': Icons.check_circle_outline,
      },
      {
        'key': 'preparing',
        'title': 'Preparing',
        'subtitle': 'Chef is cooking your meal',
        'icon': Icons.restaurant_menu,
      },
      {
        'key': 'ready',
        'title': 'Ready for Pickup',
        'subtitle': 'Waiting for delivery partner',
        'icon': Icons.inventory_2_outlined,
      },
      {
        'key': 'picked_up',
        'title': 'Picked Up',
        'subtitle': 'On the way to you',
        'icon': Icons.motorcycle,
      },
      {
        'key': 'delivered',
        'title': 'Delivered',
        'subtitle': 'Enjoy your meal!',
        'icon': Icons.home,
      },
    ];

    return Container(
      margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
      padding: EdgeInsets.all(4.w),
      decoration: BoxDecoration(
        color: Theme.of(context).cardColor,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withAlpha(51),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Order Status',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
          ),
          SizedBox(height: 2.h),
          ...statuses.asMap().entries.map((entry) {
            final index = entry.key;
            final status = entry.value;
            final isLast = index == statuses.length - 1;

            return _buildTimelineItem(
              context: context,
              status: status,
              isCompleted:
                  _isStatusCompleted(currentStatus, status['key'] as String),
              isCurrent: currentStatus == status['key'],
              isLast: isLast,
              timestamp:
                  _getStatusTimestamp(status['key'] as String, createdAt),
            );
          }),
        ],
      ),
    );
  }

  bool _isStatusCompleted(String currentStatus, String statusKey) {
    final statusOrder = [
      'pending',
      'confirmed',
      'preparing',
      'ready',
      'picked_up',
      'delivered'
    ];
    final currentIndex = statusOrder.indexOf(currentStatus);
    final statusIndex = statusOrder.indexOf(statusKey);

    return currentIndex > statusIndex;
  }

  String? _getStatusTimestamp(String statusKey, String? createdAt) {
    if (createdAt == null) return null;

    try {
      final baseTime = DateTime.parse(createdAt);

      switch (statusKey) {
        case 'confirmed':
          return _formatTime(baseTime.add(const Duration(minutes: 2)));
        case 'preparing':
          return _formatTime(baseTime.add(const Duration(minutes: 10)));
        case 'ready':
          return _formatTime(baseTime.add(const Duration(minutes: 25)));
        case 'picked_up':
          return _formatTime(baseTime.add(const Duration(minutes: 30)));
        case 'delivered':
          return _formatTime(baseTime.add(const Duration(minutes: 45)));
        default:
          return null;
      }
    } catch (e) {
      return null;
    }
  }

  String _formatTime(DateTime time) {
    final hour = time.hour.toString().padLeft(2, '0');
    final minute = time.minute.toString().padLeft(2, '0');
    return '$hour:$minute';
  }

  Widget _buildTimelineItem({
    required BuildContext context,
    required Map<String, dynamic> status,
    required bool isCompleted,
    required bool isCurrent,
    required bool isLast,
    String? timestamp,
  }) {
    final color = isCompleted || isCurrent
        ? AppTheme.getStatusColor(status['key'] as String)
        : Theme.of(context).colorScheme.outline;

    return Row(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Timeline indicator
        Column(
          children: [
            Container(
              width: 24,
              height: 24,
              decoration: BoxDecoration(
                color: isCompleted || isCurrent ? color : Colors.transparent,
                border: Border.all(
                  color: color,
                  width: 2,
                ),
                shape: BoxShape.circle,
              ),
              child: isCompleted
                  ? const Icon(
                      Icons.check,
                      size: 14,
                      color: Colors.white,
                    )
                  : isCurrent
                      ? Container(
                          margin: const EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: Colors.white,
                            shape: BoxShape.circle,
                          ),
                        )
                      : null,
            ),
            if (!isLast)
              Container(
                width: 2,
                height: 4.h,
                color: isCompleted
                    ? color
                    : Theme.of(context).colorScheme.outline.withAlpha(77),
              ),
          ],
        ),

        SizedBox(width: 3.w),

        // Status content
        Expanded(
          child: Padding(
            padding: EdgeInsets.only(bottom: isLast ? 0 : 2.h),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Icon(
                      status['icon'] as IconData,
                      size: 16,
                      color: color,
                    ),
                    SizedBox(width: 2.w),
                    Expanded(
                      child: Text(
                        status['title'] as String,
                        style: Theme.of(context).textTheme.bodyLarge?.copyWith(
                              fontWeight: isCompleted || isCurrent
                                  ? FontWeight.w600
                                  : FontWeight.w400,
                              color: isCompleted || isCurrent
                                  ? Theme.of(context).colorScheme.onSurface
                                  : Theme.of(context)
                                      .colorScheme
                                      .onSurfaceVariant,
                            ),
                      ),
                    ),
                    if (timestamp != null && (isCompleted || isCurrent))
                      Text(
                        timestamp,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                              color: Theme.of(context)
                                  .colorScheme
                                  .onSurfaceVariant,
                              fontWeight: FontWeight.w500,
                            ),
                      ),
                  ],
                ),
                SizedBox(height: 0.5.h),
                Text(
                  status['subtitle'] as String,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                        color: Theme.of(context).colorScheme.onSurfaceVariant,
                      ),
                ),
              ],
            ),
          ),
        ),
      ],
    );
  }
}

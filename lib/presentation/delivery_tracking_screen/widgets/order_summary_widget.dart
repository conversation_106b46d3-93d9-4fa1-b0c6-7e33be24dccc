import 'package:flutter/material.dart';
import 'package:sizer/sizer.dart';

import '../../../core/app_export.dart';
import '../../../widgets/custom_image_widget.dart';

class OrderSummaryWidget extends StatelessWidget {
  final Map<String, dynamic> orderData;

  const OrderSummaryWidget({
    super.key,
    required this.orderData,
  });

  @override
  Widget build(BuildContext context) {
    final restaurant = orderData['restaurants'];
    final totalAmount = orderData['total_amount'] ?? 0.0;

    return Container(
        margin: EdgeInsets.symmetric(horizontal: 4.w, vertical: 1.h),
        padding: EdgeInsets.all(4.w),
        decoration: BoxDecoration(
            color: Theme.of(context).cardColor,
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
                color: Theme.of(context).colorScheme.outline.withAlpha(51))),
        child: Column(crossAxisAlignment: CrossAxisAlignment.start, children: [
          // Restaurant header
          Row(children: [
            ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: CustomImageWidget(
                    imageUrl: restaurant?['image_url'] ?? '',
                    width: 12.w, 
                    height: 12.w, 
                    fit: BoxFit.cover)),
            SizedBox(width: 3.w),
            Expanded(
                child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                  Text(restaurant?['name'] ?? 'Restaurant',
                      style: Theme.of(context)
                          .textTheme
                          .titleMedium
                          ?.copyWith(fontWeight: FontWeight.bold)),
                  if (restaurant?['address'] != null)
                    Text(restaurant['address'],
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                            color:
                                Theme.of(context).colorScheme.onSurfaceVariant),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis),
                ])),
          ]),

          SizedBox(height: 2.h),

          // Order details
          Row(mainAxisAlignment: MainAxisAlignment.spaceBetween, children: [
            Text('Order #${orderData['order_number'] ?? 'Unknown'}',
                style: Theme.of(context)
                    .textTheme
                    .bodyLarge
                    ?.copyWith(fontWeight: FontWeight.w500)),
            Text('\$${totalAmount.toStringAsFixed(2)}',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    color: Theme.of(context).colorScheme.primary,
                    fontWeight: FontWeight.bold)),
          ]),

          SizedBox(height: 1.h),

          // Delivery address
          Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
            Icon(Icons.location_on_outlined,
                size: 20,
                color: Theme.of(context).colorScheme.onSurfaceVariant),
            SizedBox(width: 2.w),
            Expanded(
                child: Text(
                    orderData['delivery_address'] ?? 'No address provided',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color:
                            Theme.of(context).colorScheme.onSurfaceVariant))),
          ]),

          if (orderData['special_instructions'] != null) ...[
            SizedBox(height: 1.h),
            Row(crossAxisAlignment: CrossAxisAlignment.start, children: [
              Icon(Icons.note_outlined,
                  size: 20,
                  color: Theme.of(context).colorScheme.onSurfaceVariant),
              SizedBox(width: 2.w),
              Expanded(
                  child: Text(orderData['special_instructions'],
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                          color: Theme.of(context).colorScheme.onSurfaceVariant,
                          fontStyle: FontStyle.italic))),
            ]),
          ],
        ]));
  }
}
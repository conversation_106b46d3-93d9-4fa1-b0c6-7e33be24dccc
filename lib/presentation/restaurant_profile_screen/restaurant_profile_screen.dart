
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:foodflow/features/restaurant/providers/restaurant_profile_provider.dart';
import 'package:foodflow/widgets/custom_error_widget.dart';
import 'package:shimmer/shimmer.dart';

class RestaurantProfileScreen extends ConsumerWidget {
  const RestaurantProfileScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final restaurantProfileAsync = ref.watch(restaurantProfileProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Restaurant Profile'),
      ),
      body: restaurantProfileAsync.when(
        data: (restaurant) {
          if (restaurant == null) {
            return const Center(child: Text('No restaurant profile found.'));
          }
          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text('Name: ${restaurant.name}', style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold)),
                const SizedBox(height: 8),
                Text('Description: ${restaurant.description}'),
                const SizedBox(height: 8),
                Text('Address: ${restaurant.address}'),
                const SizedBox(height: 8),
                Text('Phone: ${restaurant.phone}'),
                const SizedBox(height: 8),
                Text('Email: ${restaurant.email}'),
                const SizedBox(height: 16),
                ElevatedButton(
                  onPressed: () {
                    // TODO: Implement edit functionality
                  },
                  child: const Text('Edit Profile'),
                ),
              ],
            ),
          );
        },
        loading: () => Shimmer.fromColors(
          baseColor: Colors.grey[300]!,
          highlightColor: Colors.grey[100]!,
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(height: 20, width: 200, color: Colors.white),
                const SizedBox(height: 8),
                Container(height: 15, width: 250, color: Colors.white),
                const SizedBox(height: 8),
                Container(height: 15, width: 180, color: Colors.white),
                const SizedBox(height: 8),
                Container(height: 15, width: 100, color: Colors.white),
                const SizedBox(height: 8),
                Container(height: 15, width: 150, color: Colors.white),
              ],
            ),
          ),
        ),
        error: (error, stackTrace) => CustomErrorWidget(error.toString()),
      ),
    );
  }
}

import 'package:flutter/material.dart';

import '../../../core/app_export.dart';

class EmptyOrdersWidget extends StatelessWidget {
  const EmptyOrdersWidget({super.key});

  @override
  Widget build(BuildContext context) {
    return Center(
      child: Padding(
        padding: const EdgeInsets.all(32),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 120,
              height: 120,
              decoration: BoxDecoration(
                color: AppTheme.lightTheme.colorScheme.primaryContainer,
                borderRadius: BorderRadius.circular(60),
              ),
              child: CustomIconWidget(
                iconName: 'restaurant_menu',
                size: 60,
                color: AppTheme.lightTheme.colorScheme.primary,
              ),
            ),
            const SizedBox(height: 24),
            Text(
              'No Orders Yet',
              style: AppTheme.lightTheme.textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 8),
            Text(
              'When customers place orders, they\'ll appear here. Make sure your restaurant is open to receive new orders.',
              style: AppTheme.lightTheme.textTheme.bodyMedium?.copyWith(
                color: AppTheme.lightTheme.colorScheme.onSurfaceVariant,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 32),
            ElevatedButton.icon(
              onPressed: () {
                // Navigate to menu management or promotion features
                ScaffoldMessenger.of(context).showSnackBar(
                  const SnackBar(
                    content:
                        Text('Promote your restaurant feature coming soon!'),
                  ),
                );
              },
              icon: CustomIconWidget(
                iconName: 'campaign',
                size: 20,
                color: Colors.white,
              ),
              label: const Text('Promote Your Restaurant'),
              style: ElevatedButton.styleFrom(
                padding:
                    const EdgeInsets.symmetric(horizontal: 24, vertical: 16),
              ),
            ),
            const SizedBox(height: 16),
            TextButton.icon(
              onPressed: () {
                // Navigate to menu management
                Navigator.pushNamed(context, '/menu-management-screen');
              },
              icon: CustomIconWidget(
                iconName: 'edit_note',
                size: 20,
                color: AppTheme.lightTheme.colorScheme.primary,
              ),
              label: const Text('Manage Menu'),
            ),
          ],
        ),
      ),
    );
  }
}


import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:foodflow/features/rider/providers/rider_order_provider.dart';
import 'package:foodflow/widgets/custom_error_widget.dart';
import 'package:shimmer/shimmer.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class AvailableDeliveriesScreen extends ConsumerWidget {
  const AvailableDeliveriesScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final availableOrdersAsync = ref.watch(availableOrdersProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Available Deliveries'),
      ),
      body: availableOrdersAsync.when(
        data: (orders) {
          if (orders.isEmpty) {
            return const Center(child: Text('No available deliveries.'));
          }
          return ListView.builder(
            itemCount: orders.length,
            itemBuilder: (context, index) {
              final order = orders[index];
              return ListTile(
                title: Text('Order #${order.orderNumber}'),
                subtitle: Text('From: ${order.restaurantId} - To: ${order.deliveryAddress}'),
                trailing: ElevatedButton(
                  onPressed: () async {
                    final userId = Supabase.instance.client.auth.currentUser?.id;
                    if (userId != null) {
                      await ref.read(orderServiceProvider).assignRiderToOrder(order.id, userId);
                      ScaffoldMessenger.of(context).showSnackBar(
                        const SnackBar(content: Text('Order assigned to you!')),
                      );
                    }
                  },
                  child: const Text('Accept'),
                ),
              );
            },
          );
        },
        loading: () => Shimmer.fromColors(
          baseColor: Colors.grey[300]!,
          highlightColor: Colors.grey[100]!,
          child: ListView.builder(
            itemCount: 5,
            itemBuilder: (context, index) => ListTile(
              title: Container(height: 20, color: Colors.white),
              subtitle: Container(height: 15, color: Colors.white),
            ),
          ),
        ),
        error: (error, stackTrace) => CustomErrorWidget(error.toString()),
      ),
    );
  }
}

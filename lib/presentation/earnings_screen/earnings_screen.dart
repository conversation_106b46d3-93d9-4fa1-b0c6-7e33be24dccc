
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:foodflow/features/rider/providers/rider_earnings_provider.dart';
import 'package:foodflow/widgets/custom_error_widget.dart';
import 'package:shimmer/shimmer.dart';

class EarningsScreen extends ConsumerWidget {
  const EarningsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final earningsAsync = ref.watch(riderEarningsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('My Earnings'),
      ),
      body: earningsAsync.when(
        data: (earnings) {
          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Total Deliveries: ${earnings.totalDeliveries}',
                  style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                Text(
                  'Total Earnings: ${earnings.totalEarnings.toStringAsFixed(2)}',
                  style: const TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 16),
                const Text('Delivery History:', style: TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
                Expanded(
                  child: ListView.builder(
                    itemCount: earnings.deliveryHistory.length,
                    itemBuilder: (context, index) {
                      final order = earnings.deliveryHistory[index];
                      return ListTile(
                        title: Text('Order #${order.orderNumber}'),
                        subtitle: Text('Status: ${order.status} - Amount: ${order.totalAmount.toStringAsFixed(2)}'),
                      );
                    },
                  ),
                ),
              ],
            ),
          );
        },
        loading: () => Shimmer.fromColors(
          baseColor: Colors.grey[300]!,
          highlightColor: Colors.grey[100]!,
          child: Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Container(height: 20, width: 200, color: Colors.white),
                const SizedBox(height: 8),
                Container(height: 20, width: 250, color: Colors.white),
                const SizedBox(height: 16),
                Container(height: 15, width: 150, color: Colors.white),
                Expanded(
                  child: ListView.builder(
                    itemCount: 3,
                    itemBuilder: (context, index) => ListTile(
                      title: Container(height: 20, color: Colors.white),
                      subtitle: Container(height: 15, color: Colors.white),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        error: (error, stackTrace) => CustomErrorWidget(error.toString()),
      ),
    );
  }
}

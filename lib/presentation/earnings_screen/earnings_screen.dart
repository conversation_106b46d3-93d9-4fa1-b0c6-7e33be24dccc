import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:foodflow/features/rider/providers/rider_earnings_provider.dart';
import 'package:foodflow/widgets/custom_error_widget.dart';

class EarningsScreen extends ConsumerWidget {
  const EarningsScreen({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final earningsAsync = ref.watch(riderEarningsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('My Earnings'),
      ),
      body: earningsAsync.when(
        data: (earnings) {
          return Padding(
            padding: const EdgeInsets.all(16.0),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Total Deliveries: ${earnings.totalDeliveries}',
                  style: const TextStyle(
                      fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 8),
                Text(
                  'Total Earnings: ${earnings.totalEarnings.toStringAsFixed(2)}',
                  style: const TextStyle(
                      fontSize: 18, fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 16),
                const Text('Delivery History:',
                    style:
                        TextStyle(fontSize: 16, fontWeight: FontWeight.bold)),
                Expanded(
                  child: ListView.builder(
                    itemCount: earnings.deliveryHistory.length,
                    itemBuilder: (context, index) {
                      final order = earnings.deliveryHistory[index];
                      return ListTile(
                        title: Text('Order #${order.id.substring(0, 8)}'),
                        subtitle: Text(
                            'Status: ${order.status} - Amount: \$${order.totalAmount.toStringAsFixed(2)}'),
                      );
                    },
                  ),
                ),
              ],
            ),
          );
        },
        loading: () => const Center(child: CircularProgressIndicator()),
        error: (error, stackTrace) => CustomErrorWidget(error.toString()),
      ),
    );
  }
}

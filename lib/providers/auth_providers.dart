import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:foodflow/models/user_profile.dart';
import 'package:foodflow/services/auth_service.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

final supabaseClientProvider = Provider<SupabaseClient>((ref) {
  return Supabase.instance.client;
});

final authRepositoryProvider = Provider<AuthService>((ref) {
  final client = ref.watch(supabaseClientProvider);
  return AuthService(client);
});

final authStateProvider = StreamProvider<User?>((ref) {
  final authRepository = ref.watch(authRepositoryProvider);
  return authRepository.authStateChanges;
});

final userProvider = StateNotifierProvider<UserNotifier, UserProfile?>((ref) {
  final authState = ref.watch(authStateProvider);
  final authRepository = ref.watch(authRepositoryProvider);

  return UserNotifier(authRepository, authState.value);
});

class UserNotifier extends StateNotifier<UserProfile?> {
  final AuthService _authService;
  final User? _user;

  UserNotifier(this._authService, this._user) : super(null) {
    _getProfile();
  }

  Future<void> _getProfile() async {
    if (_user != null) {
      state = await _authService.getProfile(_user!.id);
    }
  }

  Future<void> updateProfile(UserProfile profile) async {
    state = await _authService.updateProfile(profile);
  }
}

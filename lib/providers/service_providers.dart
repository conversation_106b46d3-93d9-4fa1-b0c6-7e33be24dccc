import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:foodflow/services/auth_service.dart';
import 'package:foodflow/services/order_service.dart';
import 'package:foodflow/services/restaurant_service.dart';
import 'package:foodflow/services/menu_service.dart';

// Supabase client provider
final supabaseClientProvider = Provider<SupabaseClient>((ref) {
  return Supabase.instance.client;
});

// Service providers
final authServiceProvider = Provider<AuthService>((ref) {
  final client = ref.watch(supabaseClientProvider);
  return AuthService(client);
});

final orderServiceProvider = Provider<OrderService>((ref) {
  final client = ref.watch(supabaseClientProvider);
  return OrderService(client);
});

final restaurantServiceProvider = Provider<RestaurantService>((ref) {
  final client = ref.watch(supabaseClientProvider);
  return RestaurantService(client);
});

final menuServiceProvider = Provider<MenuService>((ref) {
  final client = ref.watch(supabaseClientProvider);
  return MenuService(client);
});

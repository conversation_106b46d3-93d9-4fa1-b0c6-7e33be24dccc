import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:supabase_flutter/supabase_flutter.dart';
import 'package:foodflow/models/user_profile.dart';
import 'package:foodflow/services/auth_service.dart';

// Supabase client provider
final supabaseClientProvider = Provider<SupabaseClient>((ref) {
  return Supabase.instance.client;
});

// Auth service provider
final authServiceProvider = Provider<AuthService>((ref) {
  final client = ref.watch(supabaseClientProvider);
  return AuthService(client);
});

// Auth state provider - listens to Supabase auth changes
final authStateProvider = StreamProvider<AuthState>((ref) {
  final authService = ref.watch(authServiceProvider);
  return authService.authStateChanges;
});

// Current user provider
final currentUserProvider = Provider<User?>((ref) {
  final authState = ref.watch(authStateProvider);
  return authState.when(
    data: (state) => state.session?.user,
    loading: () => null,
    error: (_, __) => null,
  );
});

// User profile provider
final userProfileProvider = FutureProvider<UserProfile?>((ref) async {
  final user = ref.watch(currentUserProvider);
  if (user == null) return null;
  
  final authService = ref.watch(authServiceProvider);
  return await authService.getProfile(user.id);
});

// Auth controller for managing authentication actions
final authControllerProvider = StateNotifierProvider<AuthController, AuthControllerState>((ref) {
  final authService = ref.watch(authServiceProvider);
  return AuthController(authService);
});

// Auth controller state
class AuthControllerState {
  final bool isLoading;
  final String? errorMessage;
  final String? successMessage;

  AuthControllerState({
    this.isLoading = false,
    this.errorMessage,
    this.successMessage,
  });

  AuthControllerState copyWith({
    bool? isLoading,
    String? errorMessage,
    String? successMessage,
  }) {
    return AuthControllerState(
      isLoading: isLoading ?? this.isLoading,
      errorMessage: errorMessage,
      successMessage: successMessage,
    );
  }
}

// Auth controller
class AuthController extends StateNotifier<AuthControllerState> {
  final AuthService _authService;

  AuthController(this._authService) : super(AuthControllerState());

  // Sign in with email and password
  Future<bool> signIn(String email, String password) async {
    state = state.copyWith(isLoading: true, errorMessage: null);
    
    try {
      final response = await _authService.signIn(email, password);
      if (response.user != null) {
        state = state.copyWith(
          isLoading: false,
          successMessage: 'Signed in successfully',
        );
        return true;
      } else {
        state = state.copyWith(
          isLoading: false,
          errorMessage: 'Sign in failed',
        );
        return false;
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        errorMessage: e.toString(),
      );
      return false;
    }
  }

  // Sign up with email, password, full name, and role
  Future<bool> signUp({
    required String email,
    required String password,
    required String fullName,
    required String role,
  }) async {
    state = state.copyWith(isLoading: true, errorMessage: null);
    
    try {
      final response = await _authService.signUp(
        email: email,
        password: password,
        fullName: fullName,
        role: role,
      );
      
      if (response.user != null) {
        state = state.copyWith(
          isLoading: false,
          successMessage: 'Account created successfully',
        );
        return true;
      } else {
        state = state.copyWith(
          isLoading: false,
          errorMessage: 'Sign up failed',
        );
        return false;
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        errorMessage: e.toString(),
      );
      return false;
    }
  }

  // Sign out
  Future<void> signOut() async {
    state = state.copyWith(isLoading: true, errorMessage: null);
    
    try {
      await _authService.signOut();
      state = state.copyWith(
        isLoading: false,
        successMessage: 'Signed out successfully',
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        errorMessage: e.toString(),
      );
    }
  }

  // Clear messages
  void clearMessages() {
    state = state.copyWith(
      errorMessage: null,
      successMessage: null,
    );
  }
}

// Helper provider to check if user is authenticated
final isAuthenticatedProvider = Provider<bool>((ref) {
  final user = ref.watch(currentUserProvider);
  return user != null;
});

// Helper provider to get user role
final userRoleProvider = Provider<String?>((ref) {
  final userProfileAsync = ref.watch(userProfileProvider);
  return userProfileAsync.when(
    data: (profile) => profile?.role,
    loading: () => null,
    error: (_, __) => null,
  );
});

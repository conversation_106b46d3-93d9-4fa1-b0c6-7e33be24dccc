class RiderProfile {
  final String id;
  final String? vehicleType;
  final String? licenseNumber;
  final String status;
  final double? currentLatitude;
  final double? currentLongitude;
  final int totalDeliveries;
  final double totalEarnings;
  final double rating;
  final bool isVerified;

  RiderProfile({
    required this.id,
    this.vehicleType,
    this.licenseNumber,
    required this.status,
    this.currentLatitude,
    this.currentLongitude,
    required this.totalDeliveries,
    required this.totalEarnings,
    required this.rating,
    required this.isVerified,
  });

  factory RiderProfile.fromMap(Map<String, dynamic> map) {
    return RiderProfile(
      id: map['id'],
      vehicleType: map['vehicle_type'],
      licenseNumber: map['license_number'],
      status: map['status'] ?? 'offline',
      currentLatitude: map['current_latitude']?.toDouble(),
      currentLongitude: map['current_longitude']?.toDouble(),
      totalDeliveries: map['total_deliveries'] ?? 0,
      totalEarnings: (map['total_earnings'] as num?)?.toDouble() ?? 0.0,
      rating: (map['rating'] as num?)?.toDouble() ?? 0.0,
      isVerified: map['is_verified'] ?? false,
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'vehicle_type': vehicleType,
      'license_number': licenseNumber,
      'status': status,
      'current_latitude': currentLatitude,
      'current_longitude': currentLongitude,
      'total_deliveries': totalDeliveries,
      'total_earnings': totalEarnings,
      'rating': rating,
      'is_verified': isVerified,
    };
  }

  RiderProfile copyWith({
    String? id,
    String? vehicleType,
    String? licenseNumber,
    String? status,
    double? currentLatitude,
    double? currentLongitude,
    int? totalDeliveries,
    double? totalEarnings,
    double? rating,
    bool? isVerified,
  }) {
    return RiderProfile(
      id: id ?? this.id,
      vehicleType: vehicleType ?? this.vehicleType,
      licenseNumber: licenseNumber ?? this.licenseNumber,
      status: status ?? this.status,
      currentLatitude: currentLatitude ?? this.currentLatitude,
      currentLongitude: currentLongitude ?? this.currentLongitude,
      totalDeliveries: totalDeliveries ?? this.totalDeliveries,
      totalEarnings: totalEarnings ?? this.totalEarnings,
      rating: rating ?? this.rating,
      isVerified: isVerified ?? this.isVerified,
    );
  }
}

class Order {
  final String id;
  final String customerId;
  final String restaurantId;
  final String? riderId;
  final String orderNumber;
  final String status;
  final double subtotal;
  final double deliveryFee;
  final double taxAmount;
  final double totalAmount;
  final String deliveryAddress;
  final String? deliveryInstructions;
  final String? paymentMethod;
  final String paymentStatus;
  final DateTime? estimatedDeliveryTime;
  final DateTime? actualDeliveryTime;
  final String? specialInstructions;
  final DateTime createdAt;
  final DateTime updatedAt;

  Order({
    required this.id,
    required this.customerId,
    required this.restaurantId,
    this.riderId,
    required this.orderNumber,
    required this.status,
    required this.subtotal,
    required this.deliveryFee,
    required this.taxAmount,
    required this.totalAmount,
    required this.deliveryAddress,
    this.deliveryInstructions,
    this.paymentMethod,
    required this.paymentStatus,
    this.estimatedDeliveryTime,
    this.actualDeliveryTime,
    this.specialInstructions,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Order.fromMap(Map<String, dynamic> map) {
    return Order(
      id: map['id'],
      customerId: map['customer_id'],
      restaurantId: map['restaurant_id'],
      riderId: map['rider_id'],
      orderNumber: map['order_number'],
      status: map['status'],
      subtotal: (map['subtotal'] as num).toDouble(),
      deliveryFee: (map['delivery_fee'] as num).toDouble(),
      taxAmount: (map['tax_amount'] as num).toDouble(),
      totalAmount: (map['total_amount'] as num).toDouble(),
      deliveryAddress: map['delivery_address'],
      deliveryInstructions: map['delivery_instructions'],
      paymentMethod: map['payment_method'],
      paymentStatus: map['payment_status'] ?? 'pending',
      estimatedDeliveryTime: map['estimated_delivery_time'] != null
          ? DateTime.parse(map['estimated_delivery_time'])
          : null,
      actualDeliveryTime: map['actual_delivery_time'] != null
          ? DateTime.parse(map['actual_delivery_time'])
          : null,
      specialInstructions: map['special_instructions'],
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'customer_id': customerId,
      'restaurant_id': restaurantId,
      'rider_id': riderId,
      'order_number': orderNumber,
      'status': status,
      'subtotal': subtotal,
      'delivery_fee': deliveryFee,
      'tax_amount': taxAmount,
      'total_amount': totalAmount,
      'delivery_address': deliveryAddress,
      'delivery_instructions': deliveryInstructions,
      'payment_method': paymentMethod,
      'payment_status': paymentStatus,
      'estimated_delivery_time': estimatedDeliveryTime?.toIso8601String(),
      'actual_delivery_time': actualDeliveryTime?.toIso8601String(),
      'special_instructions': specialInstructions,
    };
  }

  Order copyWith({
    String? id,
    String? customerId,
    String? restaurantId,
    String? riderId,
    String? orderNumber,
    String? status,
    double? subtotal,
    double? deliveryFee,
    double? taxAmount,
    double? totalAmount,
    String? deliveryAddress,
    String? deliveryInstructions,
    String? paymentMethod,
    String? paymentStatus,
    DateTime? estimatedDeliveryTime,
    DateTime? actualDeliveryTime,
    String? specialInstructions,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Order(
      id: id ?? this.id,
      customerId: customerId ?? this.customerId,
      restaurantId: restaurantId ?? this.restaurantId,
      riderId: riderId ?? this.riderId,
      orderNumber: orderNumber ?? this.orderNumber,
      status: status ?? this.status,
      subtotal: subtotal ?? this.subtotal,
      deliveryFee: deliveryFee ?? this.deliveryFee,
      taxAmount: taxAmount ?? this.taxAmount,
      totalAmount: totalAmount ?? this.totalAmount,
      deliveryAddress: deliveryAddress ?? this.deliveryAddress,
      deliveryInstructions: deliveryInstructions ?? this.deliveryInstructions,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      paymentStatus: paymentStatus ?? this.paymentStatus,
      estimatedDeliveryTime:
          estimatedDeliveryTime ?? this.estimatedDeliveryTime,
      actualDeliveryTime: actualDeliveryTime ?? this.actualDeliveryTime,
      specialInstructions: specialInstructions ?? this.specialInstructions,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

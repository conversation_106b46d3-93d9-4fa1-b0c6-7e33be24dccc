class Restaurant {
  final String id;
  final String ownerId;
  final String name;
  final String? description;
  final String address;
  final String? phone;
  final String? email;
  final String? imageUrl;
  final String status;
  final double rating;
  final double deliveryFee;
  final double minimumOrder;
  final int deliveryTimeMinutes;
  final String? cuisineType;
  final bool isFeatured;
  final DateTime createdAt;
  final DateTime updatedAt;

  Restaurant({
    required this.id,
    required this.ownerId,
    required this.name,
    this.description,
    required this.address,
    this.phone,
    this.email,
    this.imageUrl,
    required this.status,
    required this.rating,
    required this.deliveryFee,
    required this.minimumOrder,
    required this.deliveryTimeMinutes,
    this.cuisineType,
    required this.isFeatured,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Restaurant.fromMap(Map<String, dynamic> map) {
    return Restaurant(
      id: map['id'],
      ownerId: map['owner_id'],
      name: map['name'],
      description: map['description'],
      address: map['address'],
      phone: map['phone'],
      email: map['email'],
      imageUrl: map['image_url'],
      status: map['status'] ?? 'open',
      rating: (map['rating'] as num?)?.toDouble() ?? 0.0,
      deliveryFee: (map['delivery_fee'] as num?)?.toDouble() ?? 0.0,
      minimumOrder: (map['minimum_order'] as num?)?.toDouble() ?? 0.0,
      deliveryTimeMinutes: map['delivery_time_minutes'] ?? 30,
      cuisineType: map['cuisine_type'],
      isFeatured: map['is_featured'] ?? false,
      createdAt: DateTime.parse(map['created_at']),
      updatedAt: DateTime.parse(map['updated_at']),
    );
  }

  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'owner_id': ownerId,
      'name': name,
      'description': description,
      'address': address,
      'phone': phone,
      'email': email,
      'image_url': imageUrl,
      'status': status,
      'rating': rating,
      'delivery_fee': deliveryFee,
      'minimum_order': minimumOrder,
      'delivery_time_minutes': deliveryTimeMinutes,
      'cuisine_type': cuisineType,
      'is_featured': isFeatured,
    };
  }

  Restaurant copyWith({
    String? id,
    String? ownerId,
    String? name,
    String? description,
    String? address,
    String? phone,
    String? email,
    String? imageUrl,
    String? status,
    double? rating,
    double? deliveryFee,
    double? minimumOrder,
    int? deliveryTimeMinutes,
    String? cuisineType,
    bool? isFeatured,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Restaurant(
      id: id ?? this.id,
      ownerId: ownerId ?? this.ownerId,
      name: name ?? this.name,
      description: description ?? this.description,
      address: address ?? this.address,
      phone: phone ?? this.phone,
      email: email ?? this.email,
      imageUrl: imageUrl ?? this.imageUrl,
      status: status ?? this.status,
      rating: rating ?? this.rating,
      deliveryFee: deliveryFee ?? this.deliveryFee,
      minimumOrder: minimumOrder ?? this.minimumOrder,
      deliveryTimeMinutes: deliveryTimeMinutes ?? this.deliveryTimeMinutes,
      cuisineType: cuisineType ?? this.cuisineType,
      isFeatured: isFeatured ?? this.isFeatured,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

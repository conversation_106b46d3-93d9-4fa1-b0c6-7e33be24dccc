
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:foodflow/models/order.dart';
import 'package:foodflow/models/rider_profile.dart';
import 'package:foodflow/services/order_service.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

class RiderEarnings {
  final int totalDeliveries;
  final double totalEarnings;
  final List<Order> deliveryHistory;

  RiderEarnings({
    required this.totalDeliveries,
    required this.totalEarnings,
    required this.deliveryHistory,
  });
}

final riderEarningsProvider = FutureProvider<RiderEarnings>((ref) async {
  final orderService = ref.watch(orderServiceProvider);
  final userId = Supabase.instance.client.auth.currentUser?.id;

  if (userId == null) {
    return RiderEarnings(totalDeliveries: 0, totalEarnings: 0.0, deliveryHistory: []);
  }

  // Fetch rider profile for total deliveries and earnings
  final riderProfileResponse = await Supabase.instance.client
      .from('rider_profiles')
      .select()
      .eq('id', userId)
      .single();
  final riderProfile = RiderProfile.fromMap(riderProfileResponse);

  // Fetch completed orders for delivery history
  final completedOrders = await orderService.getCompletedOrdersByRiderId(userId);

  return RiderEarnings(
    totalDeliveries: riderProfile.totalDeliveries,
    totalEarnings: riderProfile.totalEarnings,
    deliveryHistory: completedOrders,
  );
});

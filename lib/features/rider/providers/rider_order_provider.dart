import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:foodflow/models/order.dart';
import 'package:foodflow/services/order_service.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

final orderServiceProvider = Provider<OrderService>((ref) {
  return OrderService(Supabase.instance.client);
});

final availableOrdersProvider = StreamProvider.autoDispose<List<Order>>((ref) {
  final orderService = ref.watch(orderServiceProvider);
  return orderService.subscribeToAvailableOrders();
});

final assignedOrdersProvider = StreamProvider.autoDispose<List<Order>>((ref) {
  final orderService = ref.watch(orderServiceProvider);
  final userId = Supabase.instance.client.auth.currentUser?.id;
  if (userId == null) {
    return Stream.value([]);
  }
  return orderService.subscribeToAssignedOrders(userId);
});

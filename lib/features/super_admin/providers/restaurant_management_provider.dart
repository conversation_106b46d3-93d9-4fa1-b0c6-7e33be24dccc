
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:foodflow/models/restaurant.dart';
import 'package:foodflow/services/restaurant_service.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

final restaurantManagementProvider = StateNotifierProvider<RestaurantManagementNotifier, AsyncValue<List<Restaurant>>>((ref) {
  final restaurantService = ref.watch(restaurantServiceProvider);
  return RestaurantManagementNotifier(restaurantService);
});

class RestaurantManagementNotifier extends StateNotifier<AsyncValue<List<Restaurant>>> {
  final RestaurantService _restaurantService;

  RestaurantManagementNotifier(this._restaurantService) : super(const AsyncValue.loading()) {
    _fetchRestaurants();
  }

  Future<void> _fetchRestaurants() async {
    try {
      final restaurants = await _restaurantService.getRestaurants();
      state = AsyncValue.data(restaurants);
    } catch (e, st) {
      state = AsyncValue.error(e, st);
    }
  }

  Future<void> addRestaurant(Restaurant restaurant) async {
    state = const AsyncValue.loading();
    try {
      await _restaurantService.addRestaurant(restaurant);
      await _fetchRestaurants();
    } catch (e, st) {
      state = AsyncValue.error(e, st);
    }
  }

  Future<void> updateRestaurant(Restaurant restaurant) async {
    state = const AsyncValue.loading();
    try {
      await _restaurantService.updateRestaurant(restaurant);
      await _fetchRestaurants();
    } catch (e, st) {
      state = AsyncValue.error(e, st);
    }
  }

  Future<void> deleteRestaurant(String restaurantId) async {
    state = const AsyncValue.loading();
    try {
      await _restaurantService.deleteRestaurant(restaurantId);
      await _fetchRestaurants();
    } catch (e, st) {
      state = AsyncValue.error(e, st);
    }
  }
}

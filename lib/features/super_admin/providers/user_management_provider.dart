
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:foodflow/models/user_profile.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

final userManagementProvider = StateNotifierProvider<UserManagementNotifier, AsyncValue<List<UserProfile>>>((ref) {
  return UserManagementNotifier(Supabase.instance.client);
});

class UserManagementNotifier extends StateNotifier<AsyncValue<List<UserProfile>>> {
  final SupabaseClient _supabaseClient;

  UserManagementNotifier(this._supabaseClient) : super(const AsyncValue.loading()) {
    _fetchUsers();
  }

  Future<void> _fetchUsers() async {
    try {
      final response = await _supabaseClient.from('user_profiles').select();
      final users = response.map((e) => UserProfile.fromMap(e)).toList();
      state = AsyncValue.data(users);
    } catch (e, st) {
      state = AsyncValue.error(e, st);
    }
  }

  Future<void> updateUser(UserProfile user) async {
    state = const AsyncValue.loading();
    try {
      await _supabaseClient.from('user_profiles').update(user.toMap()).eq('id', user.id);
      await _fetchUsers();
    } catch (e, st) {
      state = AsyncValue.error(e, st);
    }
  }

  Future<void> deleteUser(String userId) async {
    state = const AsyncValue.loading();
    try {
      await _supabaseClient.from('user_profiles').delete().eq('id', userId);
      await _fetchUsers();
    } catch (e, st) {
      state = AsyncValue.error(e, st);
    }
  }
}


import 'package:flutter/material.dart';
import 'package:foodflow/presentation/restaurant_management_screen/restaurant_management_screen.dart';
import 'package:foodflow/presentation/rls_policy_editor_screen/rls_policy_editor_screen.dart';
import 'package:foodflow/presentation/super_admin_dashboard/super_admin_dashboard.dart';
import 'package:foodflow/presentation/user_management_screen/user_management_screen.dart';

class SuperAdminShell extends StatelessWidget {
  const SuperAdminShell({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        title: const Text('Super Admin'),
      ),
      drawer: Drawer(
        child: ListView(
          padding: EdgeInsets.zero,
          children: [
            const DrawerHeader(
              decoration: BoxDecoration(
                color: Colors.blue,
              ),
              child: Text(
                'Super Admin Menu',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 24,
                ),
              ),
            ),
            ListTile(
              leading: const Icon(Icons.dashboard),
              title: const Text('Dashboard'),
              onTap: () {
                // Navigate to dashboard
              },
            ),
            ListTile(
              leading: const Icon(Icons.people),
              title: const Text('Users'),
              onTap: () {
                Navigator.pop(context);
                Navigator.push(context, MaterialPageRoute(builder: (context) => const UserManagementScreen()));
              },
            ),
            ListTile(
              leading: const Icon(Icons.restaurant),
              title: const Text('Restaurants'),
              onTap: () {
                Navigator.pop(context);
                Navigator.push(context, MaterialPageRoute(builder: (context) => const RestaurantManagementScreen()));
              },
            ),
            ListTile(
              leading: const Icon(Icons.security),
              title: const Text('RLS Policies'),
              onTap: () {
                Navigator.pop(context);
                Navigator.push(context, MaterialPageRoute(builder: (context) => const RlsPolicyEditorScreen()));
              },
            ),
          ],
        ),
      ),
      body: const SuperAdminDashboard(),
    );
  }
}

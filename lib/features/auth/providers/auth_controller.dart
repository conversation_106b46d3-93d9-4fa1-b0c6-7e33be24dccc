import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:foodflow/services/auth_service.dart';
import 'package:foodflow/models/user_profile.dart';
import 'package:foodflow/providers/service_providers.dart';

final authControllerProvider =
    StateNotifierProvider<AuthController, AuthState>((ref) {
  return AuthController(ref.watch(authServiceProvider));
});

class AuthController extends StateNotifier<AuthState> {
  final AuthService _authService;

  AuthController(this._authService) : super(AuthState.initial());

  Future<void> signInWithEmail(String email, String password) async {
    state = state.copyWith(isLoading: true, errorMessage: null);
    try {
      final user = await _authService.signInWithEmail(email, password);
      if (user != null) {
        final userProfile = await _authService.getProfile(user.id);
        state = state.copyWith(
          isLoading: false,
          isAuthenticated: true,
          userProfile: userProfile,
        );
      } else {
        state = state.copyWith(isLoading: false, errorMessage: 'Lo<PERSON> failed.');
      }
    } catch (e) {
      state = state.copyWith(isLoading: false, errorMessage: e.toString());
    }
  }

  Future<void> signUpWithEmail(
      String email, String password, String fullName) async {
    state = state.copyWith(isLoading: true, errorMessage: null);
    try {
      final user =
          await _authService.signUpWithEmail(email, password, fullName);
      if (user != null) {
        final userProfile = await _authService.getProfile(user.id);
        state = state.copyWith(
          isLoading: false,
          isAuthenticated: true,
          userProfile: userProfile,
        );
      } else {
        state =
            state.copyWith(isLoading: false, errorMessage: 'Signup failed.');
      }
    } catch (e) {
      state = state.copyWith(isLoading: false, errorMessage: e.toString());
    }
  }

  Future<void> signOut() async {
    await _authService.signOut();
    state = AuthState.initial();
  }
}

class AuthState {
  final bool isLoading;
  final bool isAuthenticated;
  final String? errorMessage;
  final UserProfile? userProfile;

  AuthState({
    required this.isLoading,
    required this.isAuthenticated,
    this.errorMessage,
    this.userProfile,
  });

  factory AuthState.initial() {
    return AuthState(
      isLoading: false,
      isAuthenticated: false,
    );
  }

  AuthState copyWith({
    bool? isLoading,
    bool? isAuthenticated,
    String? errorMessage,
    UserProfile? userProfile,
  }) {
    return AuthState(
      isLoading: isLoading ?? this.isLoading,
      isAuthenticated: isAuthenticated ?? this.isAuthenticated,
      errorMessage: errorMessage,
      userProfile: userProfile ?? this.userProfile,
    );
  }
}


import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:foodflow/models/menu_item.dart';

class CartItem {
  final MenuItem menuItem;
  int quantity;

  CartItem({required this.menuItem, this.quantity = 1});

  double get totalPrice => menuItem.price * quantity;
}

class CartNotifier extends StateNotifier<List<CartItem>> {
  CartNotifier() : super([]);

  void addItem(MenuItem item) {
    final existingItemIndex = state.indexWhere((cartItem) => cartItem.menuItem.id == item.id);
    if (existingItemIndex != -1) {
      state = [
        for (int i = 0; i < state.length; i++)
          if (i == existingItemIndex)
            CartItem(menuItem: item, quantity: state[i].quantity + 1)
          else
            state[i],
      ];
    } else {
      state = [...state, CartItem(menuItem: item)];
    }
  }

  void removeItem(MenuItem item) {
    final existingItemIndex = state.indexWhere((cartItem) => cartItem.menuItem.id == item.id);
    if (existingItemIndex != -1) {
      if (state[existingItemIndex].quantity > 1) {
        state = [
          for (int i = 0; i < state.length; i++)
            if (i == existingItemIndex)
              CartItem(menuItem: item, quantity: state[i].quantity - 1)
            else
              state[i],
        ];
      } else {
        state = state.where((cartItem) => cartItem.menuItem.id != item.id).toList();
      }
    }
  }

  double get totalAmount {
    return state.fold(0.0, (sum, item) => sum + item.totalPrice);
  }

  void clearCart() {
    state = [];
  }
}

final cartProvider = StateNotifierProvider<CartNotifier, List<CartItem>>((ref) {
  return CartNotifier();
});

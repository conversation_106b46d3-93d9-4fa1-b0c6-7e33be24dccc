import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:foodflow/models/menu_item.dart';
import 'package:foodflow/services/menu_service.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

final menuServiceProvider = Provider<MenuService>((ref) {
  return MenuService(Supabase.instance.client);
});

final menuManagementProvider =
    StateNotifierProvider<MenuManagementNotifier, AsyncValue<List<MenuItem>>>(
        (ref) {
  final menuService = ref.watch(menuServiceProvider);
  return MenuManagementNotifier(menuService);
});

class MenuManagementNotifier extends StateNotifier<AsyncValue<List<MenuItem>>> {
  final MenuService _menuService;

  MenuManagementNotifier(this._menuService)
      : super(const AsyncValue.loading()) {
    _fetchMenuItems();
  }

  Future<void> _fetchMenuItems() async {
    try {
      final userId = Supabase.instance.client.auth.currentUser?.id;
      if (userId == null) {
        state = const AsyncValue.data([]);
        return;
      }
      // Assuming a restaurant admin only manages one restaurant
      final restaurantResponse = await Supabase.instance.client
          .from('restaurants')
          .select('id')
          .eq('owner_id', userId)
          .single();
      final restaurantId = restaurantResponse['id'];

      final menuItems = await _menuService.getMenuItems(restaurantId);
      state = AsyncValue.data(menuItems);
    } catch (e, st) {
      state = AsyncValue.error(e, st);
    }
  }

  Future<void> addMenuItem(MenuItem item) async {
    state = const AsyncValue.loading();
    try {
      await _menuService.addMenuItem(item);
      await _fetchMenuItems();
    } catch (e, st) {
      state = AsyncValue.error(e, st);
    }
  }

  Future<void> updateMenuItem(MenuItem item) async {
    state = const AsyncValue.loading();
    try {
      await _menuService.updateMenuItem(item);
      await _fetchMenuItems();
    } catch (e, st) {
      state = AsyncValue.error(e, st);
    }
  }

  Future<void> deleteMenuItem(String itemId) async {
    state = const AsyncValue.loading();
    try {
      await _menuService.deleteMenuItem(itemId);
      await _fetchMenuItems();
    } catch (e, st) {
      state = AsyncValue.error(e, st);
    }
  }
}

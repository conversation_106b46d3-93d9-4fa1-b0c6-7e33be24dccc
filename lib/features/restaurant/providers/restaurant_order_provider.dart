import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:foodflow/models/order.dart';
import 'package:foodflow/services/order_service.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

final orderServiceProvider = Provider<OrderService>((ref) {
  return OrderService(Supabase.instance.client);
});

final restaurantOrdersProvider =
    StreamProvider.autoDispose<List<Order>>((ref) async* {
  final orderService = ref.watch(orderServiceProvider);
  final userId = Supabase.instance.client.auth.currentUser?.id;

  if (userId == null) {
    yield [];
    return;
  }

  // Fetch the restaurant ID for the current user
  final restaurantResponse = await Supabase.instance.client
      .from('restaurants')
      .select('id')
      .eq('owner_id', userId)
      .single();
  final restaurantId = restaurantResponse['id'];

  // Subscribe to orders for this restaurant
  yield* orderService.subscribeToRestaurantOrders(restaurantId);
});

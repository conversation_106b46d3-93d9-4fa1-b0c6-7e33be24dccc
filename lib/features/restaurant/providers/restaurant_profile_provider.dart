
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:foodflow/services/restaurant_service.dart';
import 'package:foodflow/models/restaurant.dart';
import 'package:supabase_flutter/supabase_flutter.dart';

final restaurantProfileProvider = FutureProvider<Restaurant?>((ref) async {
  final restaurantService = ref.watch(restaurantServiceProvider);
  final userId = Supabase.instance.client.auth.currentUser?.id;
  if (userId == null) {
    return null;
  }
  return await restaurantService.getRestaurantByOwnerId(userId);
});

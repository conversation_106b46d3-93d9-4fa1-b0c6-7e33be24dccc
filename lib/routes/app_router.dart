import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:foodflow/providers/unified_auth_provider.dart';
import 'package:foodflow/features/customer/customer_shell.dart';
import 'package:foodflow/features/restaurant/restaurant_shell.dart';
import 'package:foodflow/features/rider/rider_shell.dart';
import 'package:foodflow/features/super_admin/super_admin_shell.dart';
import 'package:foodflow/routes/app_routes.dart';
import 'package:go_router/go_router.dart';

import '../presentation/delivery_tracking_screen/delivery_tracking_screen.dart';
import '../presentation/login_screen/login_screen.dart';
import '../presentation/menu_management_screen/menu_management_screen.dart';
import '../presentation/order_tracking_screen/order_tracking_screen.dart';
import '../presentation/restaurant_browse_screen/restaurant_browse_screen.dart';
import '../presentation/restaurant_dashboard/restaurant_dashboard.dart';
import '../presentation/restaurant_menu_screen/restaurant_menu_screen.dart';
import '../presentation/rider_dashboard/rider_dashboard.dart';
import '../presentation/shopping_cart_screen/shopping_cart_screen.dart';
import '../presentation/signup_screen/signup_screen.dart';
import '../presentation/super_admin_dashboard/super_admin_dashboard.dart';

final routerProvider = Provider<GoRouter>((ref) {
  final isAuthenticated = ref.watch(isAuthenticatedProvider);
  final userRole = ref.watch(userRoleProvider);

  return GoRouter(
    initialLocation: AppRoutes.initial,
    debugLogDiagnostics: true,
    routes: [
      GoRoute(
        path: AppRoutes.initial,
        builder: (context, state) => const LoginScreen(),
      ),
      GoRoute(
        path: AppRoutes.login,
        builder: (context, state) => const LoginScreen(),
      ),
      GoRoute(
        path: AppRoutes.signup,
        builder: (context, state) => const SignupScreen(),
      ),
      GoRoute(
        path: AppRoutes.superAdminDashboard,
        builder: (context, state) => const SuperAdminShell(),
      ),
      GoRoute(
        path: AppRoutes.restaurantBrowseScreen,
        builder: (context, state) => const CustomerShell(),
      ),
      GoRoute(
        path: AppRoutes.restaurantDashboard,
        builder: (context, state) => const RestaurantShell(),
      ),
      GoRoute(
        path: AppRoutes.riderDashboard,
        builder: (context, state) => const RiderShell(),
      ),
      GoRoute(
        path: AppRoutes.menuManagementScreen,
        builder: (context, state) => const MenuManagementScreen(),
      ),
      GoRoute(
        path: AppRoutes.deliveryTrackingScreen,
        builder: (context, state) => const DeliveryTrackingScreen(),
      ),
      GoRoute(
        path: '${AppRoutes.restaurantMenuScreen}/:restaurantId',
        builder: (context, state) => RestaurantMenuScreen(
          restaurantId: state.pathParameters['restaurantId']!,
        ),
      ),
      GoRoute(
        path: AppRoutes.shoppingCartScreen,
        builder: (context, state) => const ShoppingCartScreen(),
      ),
      GoRoute(
        path: '${AppRoutes.orderTrackingScreen}/:orderId',
        builder: (context, state) => OrderTrackingScreen(
          orderId: state.pathParameters['orderId']!,
        ),
      ),
    ],
    redirect: (context, state) {
      final loggingIn = state.matchedLocation == AppRoutes.login ||
          state.matchedLocation == AppRoutes.signup;

      if (!isAuthenticated) {
        return loggingIn ? null : AppRoutes.login;
      }

      if (loggingIn) {
        switch (userRole) {
          case 'super_admin':
            return AppRoutes.superAdminDashboard;
          case 'restaurant_admin':
            return AppRoutes.restaurantDashboard;
          case 'rider':
            return AppRoutes.riderDashboard;
          case 'customer':
          default:
            return AppRoutes.restaurantBrowseScreen;
        }
      }

      return null;
    },
  );
});





import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:foodflow/providers/unified_auth_provider.dart';
import 'package:foodflow/features/customer/customer_shell.dart';
import 'package:foodflow/features/restaurant/restaurant_shell.dart';
import 'package:foodflow/features/rider/rider_shell.dart';
import 'package:foodflow/features/super_admin/super_admin_shell.dart';
import 'package:foodflow/routes/app_routes.dart';
import 'package:go_router/go_router.dart';

import '../presentation/delivery_tracking_screen/delivery_tracking_screen.dart';
import '../presentation/login_screen/login_screen.dart';
import '../presentation/menu_management_screen/menu_management_screen.dart';
import '../presentation/order_tracking_screen/order_tracking_screen.dart';
import '../presentation/restaurant_browse_screen/restaurant_browse_screen.dart';
import '../presentation/restaurant_dashboard/restaurant_dashboard.dart';
import '../presentation/restaurant_menu_screen/restaurant_menu_screen.dart';
import '../presentation/rider_dashboard/rider_dashboard.dart';
import '../presentation/shopping_cart_screen/shopping_cart_screen.dart';
import '../presentation/signup_screen/signup_screen.dart';
import '../presentation/super_admin_dashboard/super_admin_dashboard.dart';

final routerProvider = Provider<GoRouter>((ref) {
  final isAuthenticated = ref.watch(isAuthenticatedProvider);
  final userRole = ref.watch(userRoleProvider);

  return GoRouter(
    initialLocation: AppRoutes.initial,
    debugLogDiagnostics: true,
    routes: [
      GoRoute(
        path: AppRoutes.initial,
        builder: (context, state) => const LoginScreen(),
      ),
      GoRoute(
        path: AppRoutes.login,
        builder: (context, state) => const LoginScreen(),
      ),
      GoRoute(
        path: AppRoutes.signup,
        builder: (context, state) => const SignupScreen(),
      ),
      GoRoute(
        path: AppRoutes.superAdminDashboard,
        builder: (context, state) => const SuperAdminShell(),
      ),
      GoRoute(
        path: AppRoutes.restaurantBrowseScreen,
        builder: (context, state) => const CustomerShell(),
      ),
      GoRoute(
        path: AppRoutes.restaurantDashboard,
        builder: (context, state) => const RestaurantShell(),
      ),
      GoRoute(
        path: AppRoutes.riderDashboard,
        builder: (context, state) => const RiderShell(),
      ),
      GoRoute(
        path: AppRoutes.menuManagementScreen,
        builder: (context, state) => const MenuManagementScreen(),
      ),
      GoRoute(
        path: AppRoutes.deliveryTrackingScreen,
        builder: (context, state) => const DeliveryTrackingScreen(),
      ),
      GoRoute(
        path: '${AppRoutes.restaurantMenuScreen}/:restaurantId',
        builder: (context, state) => RestaurantMenuScreen(
          restaurantId: state.pathParameters['restaurantId']!,
        ),
      ),
      GoRoute(
        path: AppRoutes.shoppingCartScreen,
        builder: (context, state) => const ShoppingCartScreen(),
      ),
      GoRoute(
        path: '${AppRoutes.orderTrackingScreen}/:orderId',
        builder: (context, state) => OrderTrackingScreen(
          orderId: state.pathParameters['orderId']!,
        ),
      ),
    ],
    redirect: (context, state) {
      final loggingIn = state.matchedLocation == AppRoutes.login ||
          state.matchedLocation == AppRoutes.signup;

      if (!isAuthenticated) {
        return loggingIn ? null : AppRoutes.login;
      }

      if (loggingIn) {
        switch (userRole) {
          case 'super_admin':
            return AppRoutes.superAdminDashboard;
          case 'restaurant_admin':
            return AppRoutes.restaurantDashboard;
          case 'rider':
            return AppRoutes.riderDashboard;
          case 'customer':
          default:
            return AppRoutes.restaurantBrowseScreen;
        }
      }

      return null;
    },
  );
});




Continue FoodFlow Implementation - Cross-Platform Flutter Food Delivery App

I'm building a comprehensive Flutter food delivery app with Supabase backend. Here's the current state and what needs to be completed:

Project Status:
✅ Completed:

Supabase database schema with RLS policies (all tables created)
Enhanced models (Restaurant, MenuItem, Order, OrderItem, CartItem, UserProfile, RiderProfile)
Unified authentication system with Riverpod
Login/Signup screens with role-based navigation
Basic project structure with feature-based organization
Core services (AuthService, OrderService, MenuService, RestaurantService)
🔧 Current Issues to Fix:

Multiple compilation errors in existing screens
Missing screen implementations (UserManagementScreen, RestaurantManagementScreen, RlsPolicyEditorScreen)
Incomplete shell implementations for all user roles
Missing real-time subscriptions and notifications
Image handling issues (nullable String? vs String for imageUrl fields)
Missing providers and service integrations
Project Structure:
Database Schema (Already Applied):
user_profiles (with roles: super_admin, restaurant_admin, rider, customer)
restaurants, menu_items, orders, order_items, rider_profiles
Complete RLS policies for role-based access
Sample data with test accounts for all roles
Test Accounts Available:
Super Admin: <EMAIL> / admin123
Restaurant Admin: <EMAIL> / restaurant123
Rider: <EMAIL> / rider123
Customer: <EMAIL> / customer123
Immediate Next Steps:
Fix compilation errors in existing screens (nullable image handling, missing constructors)
Complete missing screens:
UserManagementScreen (CRUD users)
RestaurantManagementScreen (CRUD restaurants)
RlsPolicyEditorScreen (basic SQL editor)
Implement complete shells for each user role with proper navigation
Add real-time features using Supabase subscriptions
Implement core functionality for each role:
Super Admin: Dashboard with KPIs, user/restaurant management
Restaurant Admin: Menu management, order queue, restaurant profile
Rider: Available deliveries, assignments, earnings tracking
Customer: Restaurant browsing, cart, checkout, order tracking
Key Files to Focus On:
 lib/providers/unified_auth_provider.dart (✅ Working auth system)
 lib/routes/app_router.dart (✅ Role-based routing setup)
Shell files in lib/features/*/ directories (need completion)
Screen files in  lib/presentation/ (many need fixes)
Technical Requirements:
Use Riverpod for state management (already set up)
Implement real-time subscriptions for orders
Add proper error handling and loading states
Include empty states and skeleton loading
Handle nullable image URLs properly throughout the app
Environment:
Supabase project: vojgdxyyydadartnyrgy.supabase.co
Database schema applied and working
Flutter project configured with all dependencies
Please continue from where we left off, focusing on fixing the compilation errors first, then implementing the missing screens and completing the user role functionality. The foundation is solid - we just need to complete the implementation.